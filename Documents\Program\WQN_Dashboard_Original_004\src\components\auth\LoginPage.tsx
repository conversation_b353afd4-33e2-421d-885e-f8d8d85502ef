import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Lock, Mail, AlertCircle } from 'lucide-react';
import { authService } from '../../services/authService';
import logoImage from '../../assets/images/logo.png';
import loginBgImage from '../../assets/images/login_bg.jpg';

// LoginPage component handles user authentication
const LoginPage: React.FC = () => {
  const [identifier, setIdentifier] = useState(''); // Stores email or username
  const [password, setPassword] = useState(''); // Stores password
  const [rememberMe, setRememberMe] = useState(false); // Stores remember me checkbox state
  const [error, setError] = useState(''); // Stores error messages
  const [isLoading, setIsLoading] = useState(false); // Indicates loading state during login
  const navigate = useNavigate(); // Hook to programmatically navigate

  // Effect to retrieve remembered credentials on component mount
  useEffect(() => {
    const remembered = authService.getRememberedCredentials(); // Fetch remembered credentials
    if (remembered) {
      setIdentifier(remembered.identifier); // Set identifier if remembered
      setRememberMe(remembered.rememberMe); // Set rememberMe state if remembered
    }
  }, []);

  // Handles form submission for login
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (isLoading) return;
    
    setError('');
    setIsLoading(true);
    
    try {
      const response = await authService.login({ 
        identifier, 
        password,
        rememberMe
      });
      const { user } = response;
      navigate(user.role === 'admin' ? '/admin' : '/user');
    } catch (error: any) {
      // Log error without sensitive data in production
      if (process.env.NODE_ENV !== 'production') {
        console.error('Login error:', error);
      }
      setError(
        error.response?.status === 401 ? 'Invalid username/email or password' :
        error.response?.status === 404 ? 'User not found' :
        !error.response ? 'Network error. Please check your connection.' :
        'An error occurred. Please try again.'
      );
      setPassword('');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes with error fade out animation
  const handleInputChange = (setter: React.Dispatch<React.SetStateAction<string>>, value: string) => {
    setter(value);
    if (error) setError('');
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: `url(${loginBgImage})`, // Background image for the login page
      }}>
      <div className="absolute inset-0 bg-black/40 backdrop-blur-sm"></div>
      
      <div className="relative w-full max-w-md p-8 bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl mx-4">
        <div className="flex flex-col items-center mb-8">
          <div className="p-3 bg-white-100 rounded-full mb-0">
            <img src={logoImage} alt="Logo" className="w-512 h-8" /> {/* Logo image */}
          </div>
          <h2 className="text-2xl font-bold text-gray-900">Water Quality Monitor</h2>
          <p className="text-gray-600 mt-2">Sign in to your account</p>
        </div>

        {error && (
          <div className="error-message mb-6 p-3 bg-red-50 border border-red-200 rounded-md flex items-center text-red-800 transition-opacity duration-300">
            <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
            <span className="text-sm">{error}</span>
          </div>
        )}

        <form 
          onSubmit={handleSubmit}
          className="space-y-6" 
          noValidate
        >
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email or Username
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={identifier}
                onChange={(e) => handleInputChange(setIdentifier, e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  error ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter your email or username"
                required
                disabled={isLoading}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="password"
                value={password}
                onChange={(e) => handleInputChange(setPassword, e.target.value)}
                className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  error ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter your password"
                required
              />
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label className="ml-2 block text-sm text-gray-700">
              Remember me
            </label>
          </div>

          <button
            type="submit"
            className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors disabled:opacity-50"
            disabled={isLoading}
          >
            {isLoading ? 'Signing in...' : 'Sign in'}
          </button>
        </form>

        <div className="mt-6 text-center text-sm">
          <span className="text-gray-600">Need an account? </span>
          <button className="font-medium text-blue-600 hover:text-blue-500">
            <a href="mailto:<EMAIL>" className="font-medium text-blue-600 hover:text-blue-500">
              Contact administrator
            </a>
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;