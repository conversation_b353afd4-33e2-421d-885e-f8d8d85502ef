import React, { useEffect, useState, FormEvent } from 'react'; // Added FormEvent
import { useNavigate } from 'react-router-dom';
import { User, Mail, Shield, Activity, Clock, Lock, CheckCircle, XCircle, ArrowLeft } from 'lucide-react'; // Added Lock, CheckCircle, XCircle, ArrowLeft
import { api } from '../../api/client';
import { useAuth } from '../../context/AuthContext';
import { Header } from '../shared/Header';

// Define the structure of user data
interface UserData {
  username: string; // The username of the user
  fullName: string; // The full name of the user
  email: string; // The email address of the user
  role: string; // The role of the user (e.g., admin, user)
  status: string; // The account status of the user (e.g., active, inactive)
  lastActive: string; // The last active timestamp of the user
}

// UserProfile component to display user information
const UserProfile: React.FC = () => {
  const navigate = useNavigate(); // Hook to programmatically navigate
  const { refreshPermissions } = useAuth(); // Get refreshPermissions function from auth context
  const [userData, setUserData] = useState<UserData | null>(null); // State to hold user data
  const [isLoading, setIsLoading] = useState(true); // State to manage loading status

  // Password Change State
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordLoading, setPasswordLoading] = useState(false);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);


  // Fetch user data on component mount
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await api.getUserProfile(); // API call to fetch user profile
        setUserData(response.data); // Set user data in state
      } catch (error) {
        console.error('Failed to fetch user data:', error); // Log error if fetching fails
        navigate('/user'); // Navigate to user page on error
      } finally {
        setIsLoading(false); // Set loading to false after fetching
      }
    };

    fetchUserData(); // Call the fetch function
  }, [navigate]); // Dependency array includes navigate

  // Handle Password Change Submission
  const handleChangePassword = async (e: FormEvent) => {
    e.preventDefault();
    setPasswordError(null);
    setPasswordSuccess(null);

    if (newPassword !== confirmPassword) {
      setPasswordError('New passwords do not match.');
      return;
    }
    // Validate password requirements to match backend
    if (newPassword.length < 8) {
      setPasswordError('New password must be at least 8 characters long.');
      return;
    }

    if (!/[A-Z]/.test(newPassword)) {
      setPasswordError('Password must contain at least one uppercase letter.');
      return;
    }

    if (!/[a-z]/.test(newPassword)) {
      setPasswordError('Password must contain at least one lowercase letter.');
      return;
    }

    if (!/[0-9]/.test(newPassword)) {
      setPasswordError('Password must contain at least one number.');
      return;
    }

    if (!/[^A-Za-z0-9]/.test(newPassword)) {
      setPasswordError('Password must contain at least one special character.');
      return;
    }

    setPasswordLoading(true);
    try {
      await api.changePassword({ currentPassword, newPassword });
      setPasswordSuccess('Password updated successfully!');

      // Refresh user permissions after password change to ensure fresh access
      console.log('🔄 Refreshing user permissions after password change...');
      try {
        await refreshPermissions();
        console.log('✅ User permissions refreshed successfully');
      } catch (permissionError) {
        console.warn('⚠️ Failed to refresh permissions after password change:', permissionError);
        // Don't fail the password change if permission refresh fails
      }

      // Clear fields after success
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
    } catch (error: any) {
      console.error('Failed to change password:', error);
      let errorMessage = 'Failed to change password. Please try again.';

      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.details) {
        // Handle Zod validation errors
        errorMessage = error.response.data.details.join(', ');
      }

      setPasswordError(errorMessage);
    } finally {
      setPasswordLoading(false);
    }
  };

  // Handle user logout
  const handleLogout = () => {
    navigate('/login'); // Navigate to login page on logout
  };

  // Handle back navigation
  const handleBack = () => {
    navigate('/user');
  };

  // Show loading spinner while data is being fetched
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Render user profile information
  return (
    <div className="min-h-screen bg-gray-50">
      <Header
        userFullName={userData?.fullName || ''}
        onLogout={handleLogout}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center space-x-4 mb-8">
          <button
            onClick={handleBack}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
            title="Back to Sensor List"
          >
            <ArrowLeft className="w-5 h-5" />
            <span className="text-sm font-medium">Back</span>
          </button>
          <h2 className="text-2xl font-bold text-gray-900">User Profile</h2>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-8">
          <div className="flex items-center mb-8 pb-8 border-b border-gray-200">
            <div className="bg-blue-100 p-4 rounded-full mr-6">
              <User className="w-16 h-16 text-blue-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">{userData?.fullName}</h2>
              <p className="text-lg text-gray-500">@{userData?.username}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="flex items-start space-x-4">
              <Mail className="w-6 h-6 text-blue-500 mt-1" />
              <div>
                <label className="text-sm font-medium text-gray-500">Email Address</label>
                <p className="text-lg font-semibold text-gray-900">{userData?.email}</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Shield className="w-6 h-6 text-green-500 mt-1" />
              <div>
                <label className="text-sm font-medium text-gray-500">Role</label>
                <p className="text-lg font-semibold text-gray-900 capitalize">{userData?.role}</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Activity className="w-6 h-6 text-purple-500 mt-1" />
              <div>
                <label className="text-sm font-medium text-gray-500">Account Status</label>
                <p className="text-lg font-semibold text-gray-900 capitalize">{userData?.status}</p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <Clock className="w-6 h-6 text-orange-500 mt-1" />
              <div>
                <label className="text-sm font-medium text-gray-500">Last Active</label>
                <p className="text-lg font-semibold text-gray-900">
                  {new Date(userData?.lastActive || '').toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Change Password Section */}
        <div className="bg-white rounded-lg shadow-sm p-8 mt-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6 border-b pb-4">Change Password</h3>
          <form onSubmit={handleChangePassword} className="space-y-6">
            {passwordError && (
              <div className="flex items-center p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50" role="alert">
                <XCircle className="flex-shrink-0 inline w-4 h-4 me-3" />
                <div>
                  <span className="font-medium">Error:</span> {passwordError}
                </div>
              </div>
            )}
            {passwordSuccess && (
              <div className="flex items-center p-4 mb-4 text-sm text-green-800 rounded-lg bg-green-50" role="alert">
                <CheckCircle className="flex-shrink-0 inline w-4 h-4 me-3" />
                <div>
                  <span className="font-medium">Success:</span> {passwordSuccess}
                </div>
              </div>
            )}
            <div>
              <label
                htmlFor="currentPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Current Password
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                  <Lock className="w-5 h-5 text-gray-400" />
                </span>
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  required
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Enter your current password"
                />
              </div>
            </div>

            <div>
              <label
                htmlFor="newPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                New Password
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                  <Lock className="w-5 h-5 text-gray-400" />
                </span>
                <input
                  type="password"
                  id="newPassword"
                  name="newPassword"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  required
                  minLength={8}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Enter new password (min. 8 chars, uppercase, lowercase, number, special char)"
                />
              </div>
              <div className="mt-2 text-xs text-gray-600">
                <p className="font-medium mb-1">Password requirements:</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>At least 8 characters long</li>
                  <li>At least one uppercase letter (A-Z)</li>
                  <li>At least one lowercase letter (a-z)</li>
                  <li>At least one number (0-9)</li>
                  <li>At least one special character (!@#$%^&*)</li>
                </ul>
              </div>
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Confirm New Password
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                  <Lock className="w-5 h-5 text-gray-400" />
                </span>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  required
                  minLength={8}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="Confirm your new password"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={passwordLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {passwordLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                ) : (
                  'Change Password'
                )}
              </button>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
};

export default UserProfile; // Export the UserProfile component
