import React, { useState } from 'react';
import { Settings as SettingsIcon, Bell, Shield, Database, Globe, Lock } from 'lucide-react';
import { PasswordChangeForm } from './PasswordChangeForm';

// Interface representing a single setting item
interface SettingItem {
  id: string; // Unique identifier for the setting
  label: string; // Display label for the setting
  type: 'toggle' | 'number' | 'select'; // Type of the setting (toggle, number input, or select dropdown)
  value: boolean | number | string; // Current value of the setting
}

// Interface representing a section of settings
interface SettingSection {
  id: string; // Unique identifier for the section
  icon: React.ElementType; // Icon component associated with the section
  title: string; // Title of the section
  description: string; // Description of the section
  settings: SettingItem[]; // Array of settings within the section
}

// Main Settings component
function Settings() {
  // State to hold the different sections of settings
  const [settingSections, setSettingSections] = useState<SettingSection[]>([
    {
      id: 'notifications', // Section ID
      icon: Bell, // Icon for notifications
      title: 'Notifications', // Title of the section
      description: 'Configure system alerts and notifications', // Description of the section
      settings: [
        { id: 'email', label: 'Email Notifications', type: 'toggle', value: true }, // Email notification toggle
        { id: 'alerts', label: 'System Alerts', type: 'toggle', value: true }, // System alerts toggle
      ]
    },
    {
      id: 'system', // Section ID
      icon: Globe, // Icon for system settings
      title: 'System', // Title of the section
      description: 'General system settings and configurations', // Description of the section
      settings: [
        { id: 'timezone', label: 'Time Zone', type: 'select', value: 'UTC' }, // Time zone select
        { id: 'language', label: 'Language', type: 'select', value: 'English' }, // Language select
      ]
    }
  ]);

  // State for password change success/error messages
  const [passwordMessage, setPasswordMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);

  // Function to handle changes to settings
  const handleSettingChange = (sectionId: string, settingId: string, newValue: boolean | number | string) => {
    setSettingSections(sections =>
      sections.map(section => {
        if (section.id === sectionId) {
          return {
            ...section,
            settings: section.settings.map(setting => {
              if (setting.id === settingId) {
                return { ...setting, value: newValue }; // Update the setting value
              }
              return setting; // Return unchanged setting
            })
          };
        }
        return section; // Return unchanged section
      })
    );
  };

  // Handle password change success
  const handlePasswordChangeSuccess = () => {
    setPasswordMessage({ type: 'success', text: 'Password changed successfully!' });
    setTimeout(() => setPasswordMessage(null), 5000); // Clear message after 5 seconds
  };

  // Handle password change error
  const handlePasswordChangeError = (error: string) => {
    setPasswordMessage({ type: 'error', text: error });
    setTimeout(() => setPasswordMessage(null), 5000); // Clear message after 5 seconds
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center space-x-4">
        <SettingsIcon className="w-8 h-8 text-blue-600" /> {/* Settings icon */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Settings</h1> {/* Main title */}
          <p className="text-gray-600">Manage system preferences and configurations</p> {/* Subtitle */}
        </div>
      </div>

      {/* Global Password Change Message */}
      {passwordMessage && (
        <div className={`p-4 rounded-md ${
          passwordMessage.type === 'success'
            ? 'bg-green-100 border border-green-400 text-green-700'
            : 'bg-red-100 border border-red-400 text-red-700'
        }`}>
          <p>{passwordMessage.text}</p>
          <button
            onClick={() => setPasswordMessage(null)}
            className="mt-2 text-sm underline hover:no-underline"
          >
            Dismiss
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6">
        {/* Account Security Section */}
        <PasswordChangeForm
          onSuccess={handlePasswordChangeSuccess}
          onError={handlePasswordChangeError}
        />

        {settingSections.map((section) => (
          <div key={section.id} className="bg-white rounded-lg shadow"> {/* Section container */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                <section.icon className="w-6 h-6 text-blue-600" /> {/* Section icon */}
                <div>
                  <h2 className="text-lg font-medium text-gray-900">{section.title}</h2> {/* Section title */}
                  <p className="text-sm text-gray-500">{section.description}</p> {/* Section description */}
                </div>
              </div>
            </div>
            <div className="p-6 space-y-4">
              {section.settings.map((setting) => (
                <div key={setting.id} className="flex items-center justify-between"> {/* Setting item container */}
                  <label className="text-sm font-medium text-gray-700">
                    {setting.label} {/* Setting label */}
                  </label>
                  {setting.type === 'toggle' ? ( // Render toggle button
                    <button
                      onClick={() => handleSettingChange(section.id, setting.id, !setting.value)} // Toggle value
                      className={`relative inline-flex h-6 w-11 items-center rounded-full ${
                        setting.value ? 'bg-blue-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                          setting.value ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  ) : setting.type === 'number' ? ( // Render number input
                    <input
                      type="number"
                      value={setting.value as number} // Current value
                      onChange={(e) => handleSettingChange(section.id, setting.id, parseInt(e.target.value, 10))} // Update value
                      className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    />
                  ) : ( // Render select dropdown
                    <select 
                      value={setting.value as string} // Current value
                      onChange={(e) => handleSettingChange(section.id, setting.id, e.target.value)} // Update value
                      className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                    >
                      {setting.id === 'timezone' ? ( // Timezone options
                        <>
                          <option value="UTC">UTC</option>
                          <option value="GMT">GMT</option>
                          <option value="EST">EST</option>
                          <option value="PST">PST</option>
                        </>
                      ) : ( // Language options
                        <>
                          <option value="English">English</option>
                          <option value="Spanish">Spanish</option>
                          <option value="French">French</option>
                          <option value="German">German</option>
                        </>
                      )}
                    </select>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Settings; // Export the Settings component for use in other parts of the application