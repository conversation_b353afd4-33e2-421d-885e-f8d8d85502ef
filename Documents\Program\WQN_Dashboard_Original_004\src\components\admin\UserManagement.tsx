import React, { useState, useEffect } from 'react';
import { <PERSON>, EyeOff, Plus, Edit2, Trash2, ChevronLeft, ChevronRight } from 'lucide-react';
import { User } from '../../types';
import { UserDialog } from './UserDialog';
import { api } from '../../api/client';
import { formatLastActive } from '../../utils/dateFormat';

// UserManagement component for managing users in the application
const UserManagement: React.FC = () => {
  // State to hold the list of users
  const [users, setUsers] = useState<User[]>([]);
  // State to hold the search query for filtering users
  const [searchQuery, setSearchQuery] = useState('');
  // State to track if the user is currently editing a user
  const [isEditing, setIsEditing] = useState(false);
  // State to track if the user is adding a new user
  const [isAdding, setIsAdding] = useState(false);
  // State to hold the user currently being edited or added
  const [editingUser, setEditingUser] = useState<User | null>(null);
  // State to track which user's password is being shown
  const [showPasswordFor, setShowPasswordFor] = useState<string | null>(null);
  // State for loading and error handling
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  // State to track the current page for pagination
  const [currentPage, setCurrentPage] = useState(1);
  // Number of items to display per page
  const itemsPerPage = 10;
  // State to manage the visibility of passwords for users
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});

  // Effect to fetch users from the API when the component mounts
  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const response = await api.getUsers(); // Fetch users from the API
        setUsers(response.data); // Update the users state with the fetched data
      } catch (error) {
        console.error('Failed to fetch users:', error); // Log error if fetching fails
      }
    };
    fetchUsers(); // Call the fetch function
  }, []);

  // Function to handle search input changes
  const handleSearch = (query: string) => {
    setSearchQuery(query); // Update the search query state
    setCurrentPage(1); // Reset to the first page on search
  };

  // Function to handle updating a user
  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault(); // Prevent default form submission
    if (!editingUser) return; // Exit if no user is being edited

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const userToUpdate = {
        ...editingUser,
        password: editingUser.password // Include the password in the update
      };

      const response = await api.updateUser(editingUser.username, userToUpdate); // API call to update user
      setUsers(users.map(user =>
        user.username === editingUser.username ? response.data : user // Update user in state with response data
      ));
      setIsEditing(false); // Exit editing mode
      setEditingUser(null); // Clear editing user
      setSuccess('User updated successfully');
    } catch (error: any) {
      console.error('Failed to update user:', error); // Log error if updating fails
      const errorMessage = error.response?.data?.error ||
                          (error.response?.data?.details && Array.isArray(error.response.data.details)
                            ? error.response.data.details.join(', ')
                            : 'Failed to update user. Please try again.');
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle adding a new user
  const handleAddUser = async (e: React.FormEvent) => {
    e.preventDefault(); // Prevent default form submission
    if (!editingUser) return; // Exit if no user is being added

    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('Adding user:', {
        ...editingUser,
        password: '***masked***' // Mask the password in logs
      });

      const userToCreate = {
        username: editingUser.username,
        fullName: editingUser.fullName,
        email: editingUser.email,
        role: editingUser.role || 'user',
        status: 'active',
        password: editingUser.password,
        lastActive: new Date().toISOString()
      };

      const response = await api.createUser(userToCreate); // API call to create user
      console.log('User created response:', {
        ...response.data,
        password: '***masked***' // Mask the password in logs
      });

      setUsers([...users, response.data]); // Update users state with the new user
      setIsAdding(false); // Exit adding mode
      setEditingUser(null); // Clear editing user
      setSuccess('User created successfully');
    } catch (error: any) {
      console.error('Failed to add user:', error); // Log error if adding fails
      const errorMessage = error.response?.data?.error ||
                          (error.response?.data?.details && Array.isArray(error.response.data.details)
                            ? error.response.data.details.join(', ')
                            : 'Failed to create user. Please try again.');
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle deleting a user
  const handleDelete = async (username: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) { // Confirm deletion
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      try {
        await api.deleteUser(username); // API call to delete user
        setUsers(users.filter(u => u.username !== username)); // Update users state
        setSuccess('User deleted successfully');
      } catch (error: any) {
        console.error('Failed to delete user:', error); // Log error if deleting fails
        const errorMessage = error.response?.data?.error || 'Failed to delete user. Please try again.';
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Filter users based on the search query
  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Calculate pagination details
  const totalPages = Math.ceil(filteredUsers.length / itemsPerPage); // Total number of pages
  const startIndex = (currentPage - 1) * itemsPerPage; // Start index for pagination
  const paginatedUsers = filteredUsers.slice(startIndex, startIndex + itemsPerPage); // Users for the current page

  // Function to handle page changes
  const handlePageChange = (page: number) => {
    setCurrentPage(page); // Update current page
  };

  // Function to toggle password visibility
  const togglePasswordVisibility = (username: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [username]: !prev[username] // Toggle visibility for the specific user
    }));
  };

  // Function to get the display password for a user
  const getDisplayPassword = (user: User): string => {
    if (showPasswords[user.username]) {
      return user.password || '••••••••'; // Return actual password if visible
    }
    return '••••••••'; // Return masked password
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">User Management</h1>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
            <p>{error}</p>
            <button
              onClick={() => setError(null)}
              className="mt-2 text-sm underline hover:no-underline"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
            <p>{success}</p>
            <button
              onClick={() => setSuccess(null)}
              className="mt-2 text-sm underline hover:no-underline"
            >
              Dismiss
            </button>
          </div>
        )}
        <div className="flex space-x-4">
          <button
            onClick={() => {
              setEditingUser({
                username: '',
                fullName: '',
                email: '',
                role: 'user',
                status: 'active',
                password: '',
                lastActive: 'Just now',
                passwordHistory: [],
                lastPasswordChange: '',
                failedLoginAttempts: 0
              });
              setIsAdding(true); // Set adding mode
              setError(null); // Clear any previous errors
              setSuccess(null); // Clear any previous success messages
            }}
            disabled={isLoading}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-5 h-5" />
            <span>{isLoading ? 'Processing...' : 'Add User'}</span>
          </button>
          <div className="relative flex-1">
            <input
              type="text"
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)} // Handle search input change
              className="w-full px-4 py-2 border rounded-md"
            />
          </div>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white border rounded-lg">
          <thead>
            <tr className="bg-gray-50">
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Username</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Full Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Password</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Active</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200">
            {paginatedUsers.map((user) => (
              <tr key={user.username}>
                <td className="px-6 py-4 whitespace-nowrap">{user.username}</td>
                <td className="px-6 py-4 whitespace-nowrap">{user.fullName}</td>
                <td className="px-6 py-4 whitespace-nowrap">{user.email}</td>
                <td className="px-6 py-4 whitespace-nowrap">{user.role}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {user.status} {/* Display user status */}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <span className="font-mono">{getDisplayPassword(user)}</span> {/* Display password */}
                    <button
                      onClick={() => togglePasswordVisibility(user.username)} // Toggle password visibility
                      className="text-gray-400 hover:text-gray-600"
                      title={showPasswords[user.username] ? "Hide password" : "Show password"}
                    >
                      {showPasswords[user.username] ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {formatLastActive(user.lastActive)} {/* Use formatLastActive instead of formatDateTime */}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex space-x-3">
                    <button
                      onClick={() => {
                        setEditingUser(user); // Set user for editing
                        setIsEditing(true); // Set editing mode
                      }}
                      className="text-blue-600 hover:text-blue-800"
                      title="Edit user"
                    >
                      <Edit2 className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleDelete(user.username)} // Handle user deletion
                      className="text-red-600 hover:text-red-800"
                      title="Delete user"
                    >
                      <Trash2 className="h-5 w-5" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {totalPages > 1 && ( // Render pagination controls if there are multiple pages
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="flex-1 flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-700">
                  Showing <span className="font-medium">{startIndex + 1}</span> to{' '}
                  <span className="font-medium">
                    {Math.min(startIndex + itemsPerPage, filteredUsers.length)}
                  </span>{' '}
                  of <span className="font-medium">{filteredUsers.length}</span> results
                </p>
              </div>
              <div className="flex space-x-2">
                <button
                  onClick={() => handlePageChange(currentPage - 1)} // Handle previous page
                  disabled={currentPage === 1} // Disable if on the first page
                  className="relative inline-flex items-center px-2 py-2 rounded-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                <button
                  onClick={() => handlePageChange(currentPage + 1)} // Handle next page
                  disabled={currentPage === totalPages} // Disable if on the last page
                  className="relative inline-flex items-center px-2 py-2 rounded-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {(isEditing || isAdding) && editingUser && ( // Render UserDialog if editing or adding a user
        <UserDialog
          user={editingUser} // Pass the user being edited or added
          onSubmit={isAdding ? handleAddUser : handleUpdateUser} // Determine submit handler
          onCancel={() => {
            setIsEditing(false); // Exit editing mode
            setIsAdding(false); // Exit adding mode
            setEditingUser(null); // Clear editing user
          }}
          isAdding={isAdding} // Pass adding state
          setEditingUser={setEditingUser} // Pass function to set editing user
        />
      )}
    </div>
  );
};

export default UserManagement; // Export the UserManagement component for use in other parts of the application