/**
 * Main server application file that sets up and configures the Express server.
 * Handles routing, middleware, error handling, and graceful shutdown procedures.
 */

// Load environment variables FIRST before any other imports
import dotenv from 'dotenv';
dotenv.config();

// Now import other modules that depend on environment variables
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Import configuration that depends on environment variables
import { validateSecurityConfig, CORS_CONFIG, SECURITY_HEADERS_CONFIG } from './config/security';

// Import other modules
import { initializeDatabase, cleanupDatabase } from './database/init';
import routes from './api/routes';
import authRoutes from './api/authRoutes';
import sensorDataRouter from './routes/sensorData';
import sensorDataProxyRouter from './routes/sensorDataProxy';
import { errorHandler } from './middleware/errorHandler';
import { sanitizeInputs } from './middleware/sanitization';
import { authMiddleware } from './middleware/auth';
import {
  generalRateLimit,
  authRateLimit,
  passwordChangeRateLimit,
  sensorDataRateLimit,
  adminRateLimit
} from './middleware/rateLimiting';

// Convert ESM module URL to filesystem path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;
let server: any = null;

// Validate security configuration on startup
validateSecurityConfig();

// Configure security middleware with proper settings
app.use(helmet({
  contentSecurityPolicy: SECURITY_HEADERS_CONFIG.contentSecurityPolicy,
  hsts: SECURITY_HEADERS_CONFIG.hsts,
  crossOriginEmbedderPolicy: SECURITY_HEADERS_CONFIG.crossOriginEmbedderPolicy,
  crossOriginOpenerPolicy: SECURITY_HEADERS_CONFIG.crossOriginOpenerPolicy,
  crossOriginResourcePolicy: SECURITY_HEADERS_CONFIG.crossOriginResourcePolicy,
  referrerPolicy: SECURITY_HEADERS_CONFIG.referrerPolicy
}));

// Configure CORS settings with security configuration
app.use(cors(CORS_CONFIG));

// Enable compression for all responses
app.use(compression({
  level: 6, // Compression level (1-9)
  threshold: 1024, // Only compress responses larger than 1KB
  filter: (req, res) => {
    // Don't compress if the request includes a cache-control: no-transform directive
    if (req.headers['cache-control'] && req.headers['cache-control'].includes('no-transform')) {
      return false;
    }
    // Use compression filter function
    return compression.filter(req, res);
  }
}));

// Parse JSON request bodies
app.use(express.json());

// Apply general rate limiting to all API routes
app.use('/api/', generalRateLimit);

// Sanitize inputs
app.use(sanitizeInputs);

// Basic health check endpoint (no rate limiting)
app.use('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Apply authentication middleware to all /api routes except /api/auth
app.use('/api', (req, res, next) => {
  if (req.path.startsWith('/auth')) {
    return next();
  }
  authMiddleware(req, res, next);
});

// Register route handlers with specific rate limiting
app.use('/api/auth', authRateLimit, authRoutes);
app.use('/api/sensor-data', sensorDataRateLimit);

// Apply password change rate limiting specifically to the password change endpoint
app.use('/api/users/change-password', passwordChangeRateLimit);

// Apply admin rate limiting to admin endpoints
app.use('/api/users', (req, res, next) => {
  // Skip rate limiting for password change endpoint as it has its own
  if (req.path === '/change-password') {
    return next();
  }
  adminRateLimit(req, res, next);
});
app.use('/api/sensors', adminRateLimit); // Admin operations
app.use('/api/permissions', adminRateLimit); // Admin operations

// Register main routes
app.use('/api', routes);
app.use('/api', sensorDataRouter);
app.use('/api', sensorDataProxyRouter);

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  // Serve static files from the dist directory
  const distPath = join(__dirname, '../../dist');
  app.use(express.static(distPath));

  // Handle client-side routing - serve index.html for all non-API routes
  app.get('*', (req, res) => {
    // Don't serve index.html for API routes
    if (req.path.startsWith('/api/') || req.path.startsWith('/health')) {
      return res.status(404).json({ error: 'API endpoint not found' });
    }
    res.sendFile(join(distPath, 'index.html'));
  });
}

// Global error handler
app.use(errorHandler);

/**
 * Handles graceful shutdown of the server
 * Closes HTTP server and database connections in order
 * @param signal - The signal that triggered the shutdown
 */
const gracefulShutdown = async (signal: string) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);

  // Close server first to stop accepting new connections
  if (server) {
    console.log('Closing HTTP server...');
    await new Promise((resolve) => {
      server.close(resolve);
    });
    console.log('HTTP server closed');
  }

  // Then cleanup database connections
  try {
    console.log('Cleaning up database connections...');
    await cleanupDatabase();
    console.log('Database connections closed');
  } catch (error) {
    console.error('Error during database cleanup:', error);
  }

  // Exit process
  console.log('Graceful shutdown completed');
  process.exit(0);
};

/**
 * Initializes and starts the Express server
 * Sets up error handling for the server instance
 */
const startServer = async () => {
  try {
    await initializeDatabase();

    server = app.listen(PORT, '127.0.0.1', () => {
      console.log(`Server running on http://127.0.0.1:${PORT}`);
    });

    // Handle server errors
    server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`Port ${PORT} is already in use`);
        process.exit(1);
      } else {
        console.error('Server error:', error);
      }
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Register process signal handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught errors and exceptions
process.on('unhandledRejection', (error) => {
  console.error('Unhandled Rejection:', error);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION').catch(console.error);
});

// Initialize the server
startServer().catch(async (error) => {
  console.error('Server startup failed:', error);
  await cleanupDatabase();
  process.exit(1);
});