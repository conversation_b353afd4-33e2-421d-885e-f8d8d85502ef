import { api } from '../api/client';
import { User } from '../types';

/**
 * Interface for login credentials passed to authentication methods
 */
export interface LoginCredentials {
  identifier: string;  // Username or email
  password: string;    // User's password
  rememberMe: boolean; // Whether to remember login between sessions
}

/**
 * Interface for the response from authentication endpoints
 */
export interface AuthResponse {
  user: User;   // User details
  token: string; // Authentication token
}

/**
 * Interface for credentials stored in localStorage when "remember me" is enabled
 */
interface StoredCredentials {
  identifier: string;  // Username or email that was used to login
  rememberMe: boolean; // Whether "remember me" was enabled
}

/**
 * Service class that handles authentication state and operations
 * Implements the Singleton pattern to ensure only one instance exists
 */
class AuthService {
  private static instance: AuthService;
  private currentUser: User | null = null;

  private constructor() {}

  /**
   * Gets the singleton instance of AuthService
   * @returns The AuthService instance
   */
  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Authenticates a user with the provided credentials
   * @param credentials - The login credentials
   * @returns Promise resolving to the authentication response
   * @throws Error if login fails
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // Log only in development mode
      if (process.env.NODE_ENV !== 'production') {
        console.log('AuthService login attempt:', credentials.identifier);
      }

      const response = await api.login(credentials);

      if (process.env.NODE_ENV !== 'production') {
        console.log('AuthService login response:', response.data);
      }

      this.currentUser = response.data.user;
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));

      if (credentials.rememberMe) {
        localStorage.setItem('rememberedUser', JSON.stringify({
          identifier: credentials.identifier,
          rememberMe: true
        }));
      } else {
        localStorage.removeItem('rememberedUser');
      }

      return response.data;
    } catch (error) {
      // Log error without sensitive data in production
      if (process.env.NODE_ENV !== 'production') {
        console.error('AuthService login error:', error);
      }
      throw error;
    }
  }

  /**
   * Logs out the current user and clears authentication state
   */
  logout(): void {
    this.currentUser = null;
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }

  /**
   * Gets the currently authenticated user
   * @returns The current user or null if not authenticated
   */
  getCurrentUser(): User | null {
    if (!this.currentUser) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        this.currentUser = JSON.parse(storedUser);
      }
    }
    return this.currentUser;
  }

  /**
   * Checks if a user is currently authenticated
   * @returns true if authenticated, false otherwise
   */
  isAuthenticated(): boolean {
    return !!this.getCurrentUser();
  }

  /**
   * Gets the current authentication token
   * @returns The authentication token or null if not authenticated
   */
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  /**
   * Gets any stored login credentials if "remember me" was enabled
   * @returns The stored credentials or null if none exist
   */
  getRememberedCredentials(): StoredCredentials | null {
    const stored = localStorage.getItem('rememberedUser');
    return stored ? JSON.parse(stored) : null;
  }
}

export const authService = AuthService.getInstance();