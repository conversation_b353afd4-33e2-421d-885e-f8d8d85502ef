#!/usr/bin/env node

/**
 * Production Server
 * Serves the built application in production mode
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fetch from 'node-fetch';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Set production environment
process.env.NODE_ENV = 'production';

console.log('🚀 Starting Water Quality Network Dashboard in production mode...');
console.log('📍 Environment: production');
console.log('🔧 Node.js version:', process.version);

// Start the server
const serverPath = path.join(__dirname, '..', 'src', 'server', 'index.ts');
const child = spawn('npx', ['tsx', serverPath], {
  stdio: 'inherit',
  env: { ...process.env, NODE_ENV: 'production' },
  shell: true
});

child.on('error', (error) => {
  console.error('❌ Failed to start production server:', error);
  process.exit(1);
});

// Test server health after startup
setTimeout(async () => {
  try {
    console.log('\n🔍 Testing server health...');
    const response = await fetch('http://127.0.0.1:3001/health');
    if (response.ok) {
      console.log('✅ Server is healthy and responding');
      console.log('🌐 Local Access: http://127.0.0.1:3001');
      console.log('🌐 Network Access: http://0.0.0.0:3001');
      console.log('🔌 API Endpoint: http://127.0.0.1:3001/api');
      console.log('💚 Health Check: http://127.0.0.1:3001/health');
      console.log('\n📱 Access from other devices on your network:');
      console.log('   Replace 0.0.0.0 with your actual IP address');
      console.log('   Example: http://*************:3001');
      console.log('\n📝 Server is running. Press Ctrl+C to stop.');
    } else {
      console.log('⚠️ Server started but health check failed');
    }
  } catch (error) {
    console.log('⚠️ Server started but health check failed:', error.message);
  }
}, 3000);

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down production server...');
  child.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  child.kill('SIGTERM');
  process.exit(0);
});
