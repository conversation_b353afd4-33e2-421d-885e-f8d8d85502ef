import crypto from 'crypto';

/**
 * Security configuration for the application
 */

// JWT Configuration - using lazy evaluation to avoid early environment variable access
let _jwtConfig: any = null;

export const JWT_CONFIG = {
  get secret() {
    if (!_jwtConfig) {
      _jwtConfig = initializeJWTConfig();
    }
    return _jwtConfig.secret;
  },

  get expiresIn() {
    return process.env.JWT_EXPIRES_IN || '24h';
  },

  // JWT Algorithm
  algorithm: 'HS256' as const,

  // Issuer and audience for additional security
  get issuer() {
    return process.env.JWT_ISSUER || 'wqn-dashboard';
  },

  get audience() {
    return process.env.JWT_AUDIENCE || 'wqn-users';
  }
};

function initializeJWTConfig() {
  const secret = process.env.JWT_SECRET;

  if (!secret) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('JWT_SECRET environment variable is required in production');
    }

    // Generate a secure random secret for development
    const generatedSecret = crypto.randomBytes(64).toString('hex');
    console.warn('⚠️  WARNING: Using generated JWT secret. Set JWT_SECRET environment variable for production.');
    console.warn(`Generated secret: ${generatedSecret}`);
    return { secret: generatedSecret };
  }

  // Validate secret strength
  if (secret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  if (secret === 'your-secret-key' || secret === 'your-super-secret-jwt-key-change-this-in-production') {
    throw new Error('JWT_SECRET cannot use default values. Please set a secure secret.');
  }

  return { secret };
}

// Password Configuration
export const PASSWORD_CONFIG = {
  // bcrypt salt rounds
  saltRounds: parseInt(process.env.BCRYPT_ROUNDS || '12'),

  // Password requirements
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,

  // Password history (prevent reuse of last N passwords)
  historyCount: 5
};

// Session Configuration - using lazy evaluation
let _sessionConfig: any = null;

export const SESSION_CONFIG = {
  get secret() {
    if (!_sessionConfig) {
      _sessionConfig = initializeSessionConfig();
    }
    return _sessionConfig.secret;
  },

  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  get secure() {
    return process.env.NODE_ENV === 'production';
  },
  httpOnly: true,
  sameSite: 'strict' as const
};

function initializeSessionConfig() {
  const secret = process.env.SESSION_SECRET;

  if (!secret) {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('SESSION_SECRET environment variable is required in production');
    }

    // Generate a secure random secret for development
    const generatedSecret = crypto.randomBytes(64).toString('hex');
    console.warn('⚠️  WARNING: Using generated session secret. Set SESSION_SECRET environment variable for production.');
    return { secret: generatedSecret };
  }

  if (secret.length < 32) {
    throw new Error('SESSION_SECRET must be at least 32 characters long');
  }

  return { secret };
}

// Rate Limiting Configuration
export const RATE_LIMIT_CONFIG = {
  // General API rate limiting
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  },

  // Strict rate limiting for authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 login attempts per windowMs
    message: 'Too many login attempts, please try again later.',
    skipSuccessfulRequests: true,
    skipFailedRequests: false
  },

  // Rate limiting for password change
  passwordChange: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // limit each IP to 3 password changes per hour
    message: 'Too many password change attempts, please try again later.'
  }
};

// CORS Configuration - using lazy evaluation
let _corsConfig: any = null;

export const CORS_CONFIG = {
  get origin() {
    if (!_corsConfig) {
      _corsConfig = initializeCorsConfig();
    }
    return _corsConfig.origin;
  },

  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  exposedHeaders: ['X-Total-Count'],
  maxAge: 86400 // 24 hours
};

function initializeCorsConfig() {
  const origins = process.env.CORS_ORIGIN;
  if (origins) {
    return { origin: origins.split(',').map(origin => origin.trim()) };
  }

  // Default origins for development
  if (process.env.NODE_ENV === 'development') {
    return { origin: ['http://localhost:5173', 'http://127.0.0.1:5173'] };
  }

  // For production, allow same-origin requests if CORS_ORIGIN not set
  console.warn('⚠️  WARNING: CORS_ORIGIN not set, allowing same-origin requests only');
  return { origin: true }; // Allow same-origin requests
}

// Security Headers Configuration
export const SECURITY_HEADERS_CONFIG = {
  contentSecurityPolicy: process.env.NODE_ENV === 'production' ? {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:"],
      scriptSrc: ["'self'", "'unsafe-eval'"], // Allow eval for Chart.js
      connectSrc: ["'self'", "https://vip.boqucloud.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  } : false, // Disable CSP in development

  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },

  crossOriginEmbedderPolicy: false,
  crossOriginOpenerPolicy: 'same-origin',
  crossOriginResourcePolicy: 'cross-origin',

  referrerPolicy: 'strict-origin-when-cross-origin'
};

/**
 * Validate all security configurations on startup
 */
export function validateSecurityConfig(): void {
  console.log('🔒 Validating security configuration...');

  // Validate JWT configuration
  if (JWT_CONFIG.secret.length < 32) {
    throw new Error('JWT secret is too weak');
  }

  // Validate password configuration
  if (PASSWORD_CONFIG.saltRounds < 10) {
    throw new Error('bcrypt salt rounds too low (minimum 10)');
  }

  // Validate session configuration
  if (SESSION_CONFIG.secret.length < 32) {
    throw new Error('Session secret is too weak');
  }

  // Validate production-specific requirements
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.JWT_SECRET) {
      throw new Error('JWT_SECRET must be set in production');
    }

    if (!process.env.SESSION_SECRET) {
      throw new Error('SESSION_SECRET must be set in production');
    }

    if (!process.env.CORS_ORIGIN) {
      console.warn('⚠️  WARNING: CORS_ORIGIN not set in production, using default same-origin policy');
    }

    if (!SESSION_CONFIG.secure) {
      console.warn('⚠️  WARNING: Session cookies are not secure in production');
    }
  }

  console.log('✅ Security configuration validated');
}
