import { User, LogOut } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

// Interface defining the props for the Header component
interface HeaderProps {
  userFullName: string;
  onLogout: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  userFullName,
  onLogout
}) => {
  const navigate = useNavigate();
  
  return (
    <header className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">

            <img
              src="/src/assets/images/favicon.png"
              alt="Ecocoast Logo"
              className="h-8 w-auto"
            />
            <h1 className="text-xl font-semibold hidden sm:block">
              Water Quality Monitor Dashboard
            </h1>
            <h1 className="text-xl font-semibold sm:hidden">
              WQM Dashboard
            </h1>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => navigate('/user/profile')}
              className="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded inline-flex items-center transition-all duration-200"
              title={userFullName || 'User Profile'}
            >
              <User className="w-5 h-5" />
            </button>
            <button
              onClick={onLogout}
              className="bg-red-500 hover:bg-red-600 text-white p-2 rounded inline-flex items-center transition-all duration-200"
              title="Logout"
            >
              <LogOut className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};
