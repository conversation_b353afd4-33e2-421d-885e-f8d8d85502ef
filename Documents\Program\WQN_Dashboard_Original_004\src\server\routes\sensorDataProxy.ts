import { Router, Request, Response } from 'express';
import fetch from 'node-fetch';
import { processApiTimestamp } from '../../utils/dateFormat';

const router = Router();

interface SensorDataResponse {
  salinity: number;
  do: number;
  ph: number;
  temp: number;
  cond: number;
  depth: number;
  turbidity: number;
  latitude: number;
  longitude: number;
  timestamp: string;
}

/**
 * Secure server-side proxy for sensor data API calls
 * This prevents exposing API credentials in the client-side code
 */
router.get('/sensor-data/:apiId', async (req: Request, res: Response) => {
  try {
    const { apiId } = req.params;

    // Validate API ID
    if (!apiId || typeof apiId !== 'string') {
      return res.status(400).json({ error: 'Invalid API ID provided' });
    }

    // Get environment variables (server-side only)
    const appKey = process.env.VITE_API_KEY;
    const appSecret = process.env.VITE_API_SECRET;
    const account = process.env.VITE_ACCOUNT;
    const tokenUrl = process.env.VITE_TOKEN_URL;
    const monitorUrl = process.env.VITE_MONITOR_OPEN_URL;
    const sensorUrl = process.env.VITE_SENSOR_DATA_URL;

    // Check if all required environment variables are set
    if (!appKey || !appSecret || !account || !tokenUrl || !monitorUrl || !sensorUrl) {
      console.error('Required environment variables are not set');
      return res.status(500).json({ error: 'Server configuration error' });
    }

    // Get access token from the token URL
    const tokenResponse = await fetch(tokenUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        appKey,
        appSecret,
        account
      })
    });

    const tokenData = await tokenResponse.json();
    
    // Check if the token request was successful
    if (!tokenResponse.ok || tokenData.code !== '200') {
      console.error('Failed to get token:', tokenData.message);
      return res.status(500).json({ error: 'Failed to authenticate with external API' });
    }

    const access_token = tokenData.data.access_token;

    // Enable monitoring mode for the specified equipment ID
    const monitorPayload = new URLSearchParams({ equipmentId: apiId }).toString();

    const monitorResponse = await fetch(monitorUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: monitorPayload
    });

    // Check if the monitoring mode request was successful
    if (monitorResponse.status === 204) {
      console.error('Monitoring mode request was skipped by the server.');
      return res.status(500).json({ error: 'Monitoring mode request skipped' });
    }

    if (!monitorResponse.ok) {
      console.error('Failed to enable monitoring mode');
      return res.status(500).json({ error: 'Failed to enable monitoring mode' });
    }

    // Fetch sensor data from the sensor URL
    const sensorResponse = await fetch(sensorUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({ equipmentId: apiId })
    });

    const sensorDataResponse = await sensorResponse.json();
    
    // Check if the sensor data fetch was successful
    if (!sensorResponse.ok || sensorDataResponse.code !== '200') {
      console.error('Failed to fetch sensor data:', sensorDataResponse.message);
      return res.status(500).json({ error: 'Failed to fetch sensor data' });
    }

    const data = sensorDataResponse.data;
    const timestamp = data['2057461']?.time || new Date().toISOString().replace('T', ' ').split('.')[0];

    // Return the structured sensor data response
    const responseData: SensorDataResponse = {
      salinity: parseFloat(data['2057461']?.value) || 0,
      do: parseFloat(data['2057459']?.value) || 0,
      ph: parseFloat(data['2057457']?.value) || 0,
      temp: parseFloat(data['2057456']?.value) || 0,
      cond: parseFloat(data['2057455']?.value) || 0,
      depth: parseFloat(data['2057454']?.value) || 0,
      turbidity: parseFloat(data['2057453']?.value) || 0,
      latitude: parseFloat(data['2057492']?.value) || 0,
      longitude: parseFloat(data['2057491']?.value) || 0,
      timestamp: data['2057461']?.time ? processApiTimestamp(data['2057461'].time) : timestamp
    };

    res.json(responseData);

  } catch (error) {
    console.error('Error in sensor data proxy:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

export default router;
