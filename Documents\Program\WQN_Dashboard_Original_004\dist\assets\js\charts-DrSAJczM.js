var t,e=Object.defineProperty,i=(t,i,s)=>((t,i,s)=>i in t?e(t,i,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[i]=s)(t,"symbol"!=typeof i?i+"":i,s);import{r as s,R as n}from"./vendor-Div0-itt.js";import{t as o,p as r,a,i as h,f as l,b as c,c as d,d as u,e as f,g,h as p,j as m,k as x,l as b,m as _,n as y,o as v,q as w,r as k,s as M,u as S,v as O,w as D,x as P,y as T,z as C,A,B as I,C as L,D as E,E as z,F as R,G as F,H as B,I as H,J as W,K as j,L as V,M as N}from"./utils-D0z3_SD5.js";function $(t){return t+.5|0}const Y=(t,e,i)=>Math.max(Math.min(t,i),e);function U(t){return Y($(2.55*t),0,255)}function X(t){return Y($(255*t),0,255)}function q(t){return Y($(t/2.55)/100,0,1)}function K(t){return Y($(100*t),0,100)}const G={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Z=[..."0123456789ABCDEF"],Q=t=>Z[15&t],J=t=>Z[(240&t)>>4]+Z[15&t],tt=t=>(240&t)>>4==(15&t);function et(t){var e=(t=>tt(t.r)&&tt(t.g)&&tt(t.b)&&tt(t.a))(t)?Q:J;return t?"#"+e(t.r)+e(t.g)+e(t.b)+((t,e)=>t<255?e(t):"")(t.a,e):void 0}const it=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function st(t,e,i){const s=e*Math.min(i,1-i),n=(e,n=(e+t/30)%12)=>i-s*Math.max(Math.min(n-3,9-n,1),-1);return[n(0),n(8),n(4)]}function nt(t,e,i){const s=(s,n=(s+t/60)%6)=>i-i*e*Math.max(Math.min(n,4-n,1),0);return[s(5),s(3),s(1)]}function ot(t,e,i){const s=st(t,1,.5);let n;for(e+i>1&&(n=1/(e+i),e*=n,i*=n),n=0;n<3;n++)s[n]*=1-e-i,s[n]+=e;return s}function rt(t){const e=t.r/255,i=t.g/255,s=t.b/255,n=Math.max(e,i,s),o=Math.min(e,i,s),r=(n+o)/2;let a,h,l;return n!==o&&(l=n-o,h=r>.5?l/(2-n-o):l/(n+o),a=function(t,e,i,s,n){return t===n?(e-i)/s+(e<i?6:0):e===n?(i-t)/s+2:(t-e)/s+4}(e,i,s,l,n),a=60*a+.5),[0|a,h||0,r]}function at(t,e,i,s){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,s)).map(X)}function ht(t,e,i){return at(st,t,e,i)}function lt(t){return(t%360+360)%360}function ct(t){const e=it.exec(t);let i,s=255;if(!e)return;e[5]!==i&&(s=e[6]?U(+e[5]):X(+e[5]));const n=lt(+e[2]),o=+e[3]/100,r=+e[4]/100;return i="hwb"===e[1]?function(t,e,i){return at(ot,t,e,i)}(n,o,r):"hsv"===e[1]?function(t,e,i){return at(nt,t,e,i)}(n,o,r):ht(n,o,r),{r:i[0],g:i[1],b:i[2],a:s}}const dt={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},ut={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let ft;function gt(t){ft||(ft=function(){const t={},e=Object.keys(ut),i=Object.keys(dt);let s,n,o,r,a;for(s=0;s<e.length;s++){for(r=a=e[s],n=0;n<i.length;n++)o=i[n],a=a.replace(o,dt[o]);o=parseInt(ut[r],16),t[a]=[o>>16&255,o>>8&255,255&o]}return t}(),ft.transparent=[0,0,0,0]);const e=ft[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}const pt=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;const mt=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,xt=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function bt(t,e,i){if(t){let s=rt(t);s[e]=Math.max(0,Math.min(s[e]+s[e]*i,0===e?360:1)),s=ht(s),t.r=s[0],t.g=s[1],t.b=s[2]}}function _t(t,e){return t?Object.assign(e||{},t):t}function yt(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=X(t[3]))):(e=_t(t,{r:0,g:0,b:0,a:1})).a=X(e.a),e}function vt(t){return"r"===t.charAt(0)?function(t){const e=pt.exec(t);let i,s,n,o=255;if(e){if(e[7]!==i){const t=+e[7];o=e[8]?U(t):Y(255*t,0,255)}return i=+e[1],s=+e[3],n=+e[5],i=255&(e[2]?U(i):Y(i,0,255)),s=255&(e[4]?U(s):Y(s,0,255)),n=255&(e[6]?U(n):Y(n,0,255)),{r:i,g:s,b:n,a:o}}}(t):ct(t)}class wt{constructor(t){if(t instanceof wt)return t;const e=typeof t;let i;var s,n,o;"object"===e?i=yt(t):"string"===e&&(o=(s=t).length,"#"===s[0]&&(4===o||5===o?n={r:255&17*G[s[1]],g:255&17*G[s[2]],b:255&17*G[s[3]],a:5===o?17*G[s[4]]:255}:7!==o&&9!==o||(n={r:G[s[1]]<<4|G[s[2]],g:G[s[3]]<<4|G[s[4]],b:G[s[5]]<<4|G[s[6]],a:9===o?G[s[7]]<<4|G[s[8]]:255})),i=n||gt(t)||vt(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=_t(this._rgb);return t&&(t.a=q(t.a)),t}set rgb(t){this._rgb=yt(t)}rgbString(){return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${q(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0;var t}hexString(){return this._valid?et(this._rgb):void 0}hslString(){return this._valid?function(t){if(!t)return;const e=rt(t),i=e[0],s=K(e[1]),n=K(e[2]);return t.a<255?`hsla(${i}, ${s}%, ${n}%, ${q(t.a)})`:`hsl(${i}, ${s}%, ${n}%)`}(this._rgb):void 0}mix(t,e){if(t){const i=this.rgb,s=t.rgb;let n;const o=e===n?.5:e,r=2*o-1,a=i.a-s.a,h=((r*a===-1?r:(r+a)/(1+r*a))+1)/2;n=1-h,i.r=255&h*i.r+n*s.r+.5,i.g=255&h*i.g+n*s.g+.5,i.b=255&h*i.b+n*s.b+.5,i.a=o*i.a+(1-o)*s.a,this.rgb=i}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,i){const s=xt(q(t.r)),n=xt(q(t.g)),o=xt(q(t.b));return{r:X(mt(s+i*(xt(q(e.r))-s))),g:X(mt(n+i*(xt(q(e.g))-n))),b:X(mt(o+i*(xt(q(e.b))-o))),a:t.a+i*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new wt(this.rgb)}alpha(t){return this._rgb.a=X(t),this}clearer(t){return this._rgb.a*=1-t,this}greyscale(){const t=this._rgb,e=$(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){return this._rgb.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return bt(this._rgb,2,t),this}darken(t){return bt(this._rgb,2,-t),this}saturate(t){return bt(this._rgb,1,t),this}desaturate(t){return bt(this._rgb,1,-t),this}rotate(t){return function(t,e){var i=rt(t);i[0]=lt(i[0]+e),i=ht(i),t.r=i[0],t.g=i[1],t.b=i[2]}(this._rgb,t),this}}function kt(){}const Mt=(()=>{let t=0;return()=>t++})();function St(t){return null==t}function Ot(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function Dt(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function Pt(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function Tt(t,e){return Pt(t)?t:e}function Ct(t,e){return void 0===t?e:t}function At(t,e,i){if(t&&"function"==typeof t.call)return t.apply(i,e)}function It(t,e,i,s){let n,o,r;if(Ot(t))for(o=t.length,n=0;n<o;n++)e.call(i,t[n],n);else if(Dt(t))for(r=Object.keys(t),o=r.length,n=0;n<o;n++)e.call(i,t[r[n]],r[n])}function Lt(t,e){let i,s,n,o;if(!t||!e||t.length!==e.length)return!1;for(i=0,s=t.length;i<s;++i)if(n=t[i],o=e[i],n.datasetIndex!==o.datasetIndex||n.index!==o.index)return!1;return!0}function Et(t){if(Ot(t))return t.map(Et);if(Dt(t)){const e=Object.create(null),i=Object.keys(t),s=i.length;let n=0;for(;n<s;++n)e[i[n]]=Et(t[i[n]]);return e}return t}function zt(t){return-1===["__proto__","prototype","constructor"].indexOf(t)}function Rt(t,e,i,s){if(!zt(t))return;const n=e[t],o=i[t];Dt(n)&&Dt(o)?Ft(n,o,s):e[t]=Et(o)}function Ft(t,e,i){const s=Ot(e)?e:[e],n=s.length;if(!Dt(t))return t;const o=(i=i||{}).merger||Rt;let r;for(let a=0;a<n;++a){if(r=s[a],!Dt(r))continue;const e=Object.keys(r);for(let s=0,n=e.length;s<n;++s)o(e[s],t,r,i)}return t}function Bt(t,e){return Ft(t,e,{merger:Ht})}function Ht(t,e,i){if(!zt(t))return;const s=e[t],n=i[t];Dt(s)&&Dt(n)?Bt(s,n):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=Et(n))}const Wt={"":t=>t,x:t=>t.x,y:t=>t.y};function jt(t,e){const i=Wt[e]||(Wt[e]=function(t){const e=function(t){const e=t.split("."),i=[];let s="";for(const n of e)s+=n,s.endsWith("\\")?s=s.slice(0,-1)+".":(i.push(s),s="");return i}(t);return t=>{for(const i of e){if(""===i)break;t=t&&t[i]}return t}}(e));return i(t)}function Vt(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Nt=t=>void 0!==t,$t=t=>"function"==typeof t,Yt=(t,e)=>{if(t.size!==e.size)return!1;for(const i of t)if(!e.has(i))return!1;return!0};const Ut=Math.PI,Xt=2*Ut,qt=Xt+Ut,Kt=Number.POSITIVE_INFINITY,Gt=Ut/180,Zt=Ut/2,Qt=Ut/4,Jt=2*Ut/3,te=Math.log10,ee=Math.sign;function ie(t,e,i){return Math.abs(t-e)<i}function se(t){const e=Math.round(t);t=ie(t,e,t/1e3)?e:t;const i=Math.pow(10,Math.floor(te(t))),s=t/i;return(s<=1?1:s<=2?2:s<=5?5:10)*i}function ne(t){return!isNaN(parseFloat(t))&&isFinite(t)}function oe(t){return t*(Ut/180)}function re(t){if(!Pt(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function ae(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function he(t,e){return(t-e+qt)%Xt-Ut}function le(t){return(t%Xt+Xt)%Xt}function ce(t,e,i,s){const n=le(t),o=le(e),r=le(i),a=le(o-n),h=le(r-n),l=le(n-o),c=le(n-r);return n===o||n===r||s&&o===r||a>h&&l<c}function de(t,e,i){return Math.max(e,Math.min(i,t))}function ue(t,e,i,s=1e-6){return t>=Math.min(e,i)-s&&t<=Math.max(e,i)+s}function fe(t,e,i){i=i||(i=>t[i]<e);let s,n=t.length-1,o=0;for(;n-o>1;)s=o+n>>1,i(s)?o=s:n=s;return{lo:o,hi:n}}const ge=(t,e,i,s)=>fe(t,i,s?s=>{const n=t[s][e];return n<i||n===i&&t[s+1][e]===i}:s=>t[s][e]<i),pe=(t,e,i)=>fe(t,i,(s=>t[s][e]>=i));const me=["push","pop","shift","splice","unshift"];function xe(t,e){const i=t._chartjs;if(!i)return;const s=i.listeners,n=s.indexOf(e);-1!==n&&s.splice(n,1),s.length>0||(me.forEach((e=>{delete t[e]})),delete t._chartjs)}const be="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function _e(t,e){let i=[],s=!1;return function(...n){i=n,s||(s=!0,be.call(window,(()=>{s=!1,t.apply(e,i)})))}}const ye=t=>"start"===t?"left":"end"===t?"right":"center",ve=(t,e,i)=>"start"===t?e:"end"===t?i:(e+i)/2;const we=t=>0===t||1===t,ke=(t,e,i)=>-Math.pow(2,10*(t-=1))*Math.sin((t-e)*Xt/i),Me=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*Xt/i)+1,Se={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>1-Math.cos(t*Zt),easeOutSine:t=>Math.sin(t*Zt),easeInOutSine:t=>-.5*(Math.cos(Ut*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>we(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(2-Math.pow(2,-10*(2*t-1))),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>we(t)?t:ke(t,.075,.3),easeOutElastic:t=>we(t)?t:Me(t,.075,.3),easeInOutElastic(t){const e=.1125;return we(t)?t:t<.5?.5*ke(2*t,e,.45):.5+.5*Me(2*t-1,e,.45)},easeInBack(t){const e=1.70158;return t*t*((e+1)*t-e)},easeOutBack(t){const e=1.70158;return(t-=1)*t*((e+1)*t+e)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?t*t*((1+(e*=1.525))*t-e)*.5:.5*((t-=2)*t*((1+(e*=1.525))*t+e)+2)},easeInBounce:t=>1-Se.easeOutBounce(1-t),easeOutBounce(t){const e=7.5625,i=2.75;return t<1/i?e*t*t:t<2/i?e*(t-=1.5/i)*t+.75:t<2.5/i?e*(t-=2.25/i)*t+.9375:e*(t-=2.625/i)*t+.984375},easeInOutBounce:t=>t<.5?.5*Se.easeInBounce(2*t):.5*Se.easeOutBounce(2*t-1)+.5};function Oe(t){if(t&&"object"==typeof t){const e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function De(t){return Oe(t)?t:new wt(t)}function Pe(t){return Oe(t)?t:new wt(t).saturate(.5).darken(.1).hexString()}const Te=["x","y","borderWidth","radius","tension"],Ce=["color","borderColor","backgroundColor"];const Ae=new Map;function Ie(t,e,i){return function(t,e){e=e||{};const i=t+JSON.stringify(e);let s=Ae.get(i);return s||(s=new Intl.NumberFormat(t,e),Ae.set(i,s)),s}(e,i).format(t)}const Le={values:t=>Ot(t)?t:""+t,numeric(t,e,i){if(0===t)return"0";const s=this.chart.options.locale;let n,o=t;if(i.length>1){const e=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(e<1e-4||e>1e15)&&(n="scientific"),o=function(t,e){let i=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;Math.abs(i)>=1&&t!==Math.floor(t)&&(i=t-Math.floor(t));return i}(t,i)}const r=te(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),h={notation:n,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(h,this.options.ticks.format),Ie(t,s,h)},logarithmic(t,e,i){if(0===t)return"0";const s=i[e].significand||t/Math.pow(10,Math.floor(te(t)));return[1,2,3,5,10,15].includes(s)||e>.8*i.length?Le.numeric.call(this,t,e,i):""}};var Ee={formatters:Le};const ze=Object.create(null),Re=Object.create(null);function Fe(t,e){if(!e)return t;const i=e.split(".");for(let s=0,n=i.length;s<n;++s){const e=i[s];t=t[e]||(t[e]=Object.create(null))}return t}function Be(t,e,i){return"string"==typeof e?Ft(Fe(t,e),i):Ft(Fe(t,""),e)}class He{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>Pe(e.backgroundColor),this.hoverBorderColor=(t,e)=>Pe(e.borderColor),this.hoverColor=(t,e)=>Pe(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return Be(this,t,e)}get(t){return Fe(this,t)}describe(t,e){return Be(Re,t,e)}override(t,e){return Be(ze,t,e)}route(t,e,i,s){const n=Fe(this,t),o=Fe(this,i),r="_"+e;Object.defineProperties(n,{[r]:{value:n[e],writable:!0},[e]:{enumerable:!0,get(){const t=this[r],e=o[s];return Dt(t)?Object.assign({},e,t):Ct(t,e)},set(t){this[r]=t}}})}apply(t){t.forEach((t=>t(this)))}}var We=new He({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:Ce},numbers:{type:"number",properties:Te}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ee.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function je(t,e,i,s,n){let o=e[n];return o||(o=e[n]=t.measureText(n).width,i.push(n)),o>s&&(s=o),s}function Ve(t,e,i){const s=t.currentDevicePixelRatio,n=0!==i?Math.max(i/2,.5):0;return Math.round((e-n)*s)/s+n}function Ne(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function $e(t,e,i,s){Ye(t,e,i,s,null)}function Ye(t,e,i,s,n){let o,r,a,h,l,c,d,u;const f=e.pointStyle,g=e.rotation,p=e.radius;let m=(g||0)*Gt;if(f&&"object"==typeof f&&(o=f.toString(),"[object HTMLImageElement]"===o||"[object HTMLCanvasElement]"===o))return t.save(),t.translate(i,s),t.rotate(m),t.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void t.restore();if(!(isNaN(p)||p<=0)){switch(t.beginPath(),f){default:n?t.ellipse(i,s,n/2,p,0,0,Xt):t.arc(i,s,p,0,Xt),t.closePath();break;case"triangle":c=n?n/2:p,t.moveTo(i+Math.sin(m)*c,s-Math.cos(m)*p),m+=Jt,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*p),m+=Jt,t.lineTo(i+Math.sin(m)*c,s-Math.cos(m)*p),t.closePath();break;case"rectRounded":l=.516*p,h=p-l,r=Math.cos(m+Qt)*h,d=Math.cos(m+Qt)*(n?n/2-l:h),a=Math.sin(m+Qt)*h,u=Math.sin(m+Qt)*(n?n/2-l:h),t.arc(i-d,s-a,l,m-Ut,m-Zt),t.arc(i+u,s-r,l,m-Zt,m),t.arc(i+d,s+a,l,m,m+Zt),t.arc(i-u,s+r,l,m+Zt,m+Ut),t.closePath();break;case"rect":if(!g){h=Math.SQRT1_2*p,c=n?n/2:h,t.rect(i-c,s-h,2*c,2*h);break}m+=Qt;case"rectRot":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+u,s-r),t.lineTo(i+d,s+a),t.lineTo(i-u,s+r),t.closePath();break;case"crossRot":m+=Qt;case"cross":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+d,s+a),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"star":d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+d,s+a),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r),m+=Qt,d=Math.cos(m)*(n?n/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,u=Math.sin(m)*(n?n/2:p),t.moveTo(i-d,s-a),t.lineTo(i+d,s+a),t.moveTo(i+u,s-r),t.lineTo(i-u,s+r);break;case"line":r=n?n/2:Math.cos(m)*p,a=Math.sin(m)*p,t.moveTo(i-r,s-a),t.lineTo(i+r,s+a);break;case"dash":t.moveTo(i,s),t.lineTo(i+Math.cos(m)*(n?n/2:p),s+Math.sin(m)*p);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function Ue(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function Xe(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function qe(t){t.restore()}function Ke(t,e,i,s,n){if(!e)return t.lineTo(i.x,i.y);if("middle"===n){const s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else"after"===n!=!!s?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function Ge(t,e,i,s){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(s?e.cp1x:e.cp2x,s?e.cp1y:e.cp2y,s?i.cp2x:i.cp1x,s?i.cp2y:i.cp1y,i.x,i.y)}function Ze(t,e,i,s,n){if(n.strikethrough||n.underline){const o=t.measureText(s),r=e-o.actualBoundingBoxLeft,a=e+o.actualBoundingBoxRight,h=i-o.actualBoundingBoxAscent,l=i+o.actualBoundingBoxDescent,c=n.strikethrough?(h+l)/2:l;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=n.decorationWidth||2,t.moveTo(r,c),t.lineTo(a,c),t.stroke()}}function Qe(t,e){const i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}function Je(t,e,i,s,n,o={}){const r=Ot(e)?e:[e],a=o.strokeWidth>0&&""!==o.strokeColor;let h,l;for(t.save(),t.font=n.string,function(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),St(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}(t,o),h=0;h<r.length;++h)l=r[h],o.backdrop&&Qe(t,o.backdrop),a&&(o.strokeColor&&(t.strokeStyle=o.strokeColor),St(o.strokeWidth)||(t.lineWidth=o.strokeWidth),t.strokeText(l,i,s,o.maxWidth)),t.fillText(l,i,s,o.maxWidth),Ze(t,i,s,l,o),s+=Number(n.lineHeight);t.restore()}function ti(t,e){const{x:i,y:s,w:n,h:o,radius:r}=e;t.arc(i+r.topLeft,s+r.topLeft,r.topLeft,1.5*Ut,Ut,!0),t.lineTo(i,s+o-r.bottomLeft),t.arc(i+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,Ut,Zt,!0),t.lineTo(i+n-r.bottomRight,s+o),t.arc(i+n-r.bottomRight,s+o-r.bottomRight,r.bottomRight,Zt,0,!0),t.lineTo(i+n,s+r.topRight),t.arc(i+n-r.topRight,s+r.topRight,r.topRight,0,-Zt,!0),t.lineTo(i+r.topLeft,s)}const ei=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,ii=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function si(t,e){const i=(""+t).match(ei);if(!i||"normal"===i[1])return 1.2*e;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100}return e*t}function ni(t,e){const i={},s=Dt(e),n=s?Object.keys(e):e,o=Dt(t)?s?i=>Ct(t[i],t[e[i]]):e=>t[e]:()=>t;for(const r of n)i[r]=+o(r)||0;return i}function oi(t){return ni(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ri(t){const e=function(t){return ni(t,{top:"y",right:"x",bottom:"y",left:"x"})}(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ai(t,e){t=t||{},e=e||We.font;let i=Ct(t.size,e.size);"string"==typeof i&&(i=parseInt(i,10));let s=Ct(t.style,e.style);s&&!(""+s).match(ii)&&(s=void 0);const n={family:Ct(t.family,e.family),lineHeight:si(Ct(t.lineHeight,e.lineHeight),i),size:i,style:s,weight:Ct(t.weight,e.weight),string:""};return n.string=function(t){return!t||St(t.size)||St(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}(n),n}function hi(t,e,i,s){let n,o,r;for(n=0,o=t.length;n<o;++n)if(r=t[n],void 0!==r&&void 0!==r)return r}function li(t,e,i){const{min:s,max:n}=t,o=(a=(n-s)/2,"string"==typeof(r=e)&&r.endsWith("%")?parseFloat(r)/100*a:+r);var r,a;const h=(t,e)=>i&&0===t?0:t+e;return{min:h(s,-Math.abs(o)),max:h(n,o)}}function ci(t,e){return Object.assign(Object.create(t),e)}function di(t,e=[""],i,s,n=()=>t[0]){const o=i||t;void 0===s&&(s=wi("_fallback",t));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:o,_fallback:s,_getTarget:n,override:i=>di([i,...t],e,o,s)};return new Proxy(r,{deleteProperty:(e,i)=>(delete e[i],delete e._keys,delete t[0][i],!0),get:(i,s)=>mi(i,s,(()=>function(t,e,i,s){let n;for(const o of e)if(n=wi(gi(o,t),i),void 0!==n)return pi(t,n)?yi(i,s,t,n):n}(s,e,t,i))),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>ki(t).includes(e),ownKeys:t=>ki(t),set(t,e,i){const s=t._storage||(t._storage=n());return t[e]=s[e]=i,delete t._keys,!0}})}function ui(t,e,i,s){const n={_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:fi(t,s),setContext:e=>ui(t,e,i,s),override:n=>ui(t.override(n),e,i,s)};return new Proxy(n,{deleteProperty:(e,i)=>(delete e[i],delete t[i],!0),get:(t,e,i)=>mi(t,e,(()=>function(t,e,i){const{_proxy:s,_context:n,_subProxy:o,_descriptors:r}=t;let a=s[e];$t(a)&&r.isScriptable(e)&&(a=function(t,e,i,s){const{_proxy:n,_context:o,_subProxy:r,_stack:a}=i;if(a.has(t))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+t);a.add(t);let h=e(o,r||s);a.delete(t),pi(t,h)&&(h=yi(n._scopes,n,t,h));return h}(e,a,t,i));Ot(a)&&a.length&&(a=function(t,e,i,s){const{_proxy:n,_context:o,_subProxy:r,_descriptors:a}=i;if(void 0!==o.index&&s(t))return e[o.index%e.length];if(Dt(e[0])){const i=e,s=n._scopes.filter((t=>t!==i));e=[];for(const h of i){const i=yi(s,n,t,h);e.push(ui(i,o,r&&r[t],a))}}return e}(e,a,t,r.isIndexable));pi(e,a)&&(a=ui(a,n,o&&o[e],r));return a}(t,e,i))),getOwnPropertyDescriptor:(e,i)=>e._descriptors.allKeys?Reflect.has(t,i)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,i),getPrototypeOf:()=>Reflect.getPrototypeOf(t),has:(e,i)=>Reflect.has(t,i),ownKeys:()=>Reflect.ownKeys(t),set:(e,i,s)=>(t[i]=s,delete e[i],!0)})}function fi(t,e={scriptable:!0,indexable:!0}){const{_scriptable:i=e.scriptable,_indexable:s=e.indexable,_allKeys:n=e.allKeys}=t;return{allKeys:n,scriptable:i,indexable:s,isScriptable:$t(i)?i:()=>i,isIndexable:$t(s)?s:()=>s}}const gi=(t,e)=>t?t+Vt(e):e,pi=(t,e)=>Dt(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function mi(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];const s=i();return t[e]=s,s}function xi(t,e,i){return $t(t)?t(e,i):t}const bi=(t,e)=>!0===t?e:"string"==typeof t?jt(e,t):void 0;function _i(t,e,i,s,n){for(const o of e){const e=bi(i,o);if(e){t.add(e);const o=xi(e._fallback,i,n);if(void 0!==o&&o!==i&&o!==s)return o}else if(!1===e&&void 0!==s&&i!==s)return null}return!1}function yi(t,e,i,s){const n=e._rootScopes,o=xi(e._fallback,i,s),r=[...t,...n],a=new Set;a.add(s);let h=vi(a,r,i,o||i,s);return null!==h&&((void 0===o||o===i||(h=vi(a,r,o,h,s),null!==h))&&di(Array.from(a),[""],n,o,(()=>function(t,e,i){const s=t._getTarget();e in s||(s[e]={});const n=s[e];if(Ot(n)&&Dt(i))return i;return n||{}}(e,i,s))))}function vi(t,e,i,s,n){for(;i;)i=_i(t,e,i,s,n);return i}function wi(t,e){for(const i of e){if(!i)continue;const e=i[t];if(void 0!==e)return e}}function ki(t){let e=t._keys;return e||(e=t._keys=function(t){const e=new Set;for(const i of t)for(const t of Object.keys(i).filter((t=>!t.startsWith("_"))))e.add(t);return Array.from(e)}(t._scopes)),e}const Mi=Number.EPSILON||1e-14,Si=(t,e)=>e<t.length&&!t[e].skip&&t[e],Oi=t=>"x"===t?"y":"x";function Di(t,e,i,s){const n=t.skip?e:t,o=e,r=i.skip?e:i,a=ae(o,n),h=ae(r,o);let l=a/(a+h),c=h/(a+h);l=isNaN(l)?0:l,c=isNaN(c)?0:c;const d=s*l,u=s*c;return{previous:{x:o.x-d*(r.x-n.x),y:o.y-d*(r.y-n.y)},next:{x:o.x+u*(r.x-n.x),y:o.y+u*(r.y-n.y)}}}function Pi(t,e="x"){const i=Oi(e),s=t.length,n=Array(s).fill(0),o=Array(s);let r,a,h,l=Si(t,0);for(r=0;r<s;++r)if(a=h,h=l,l=Si(t,r+1),h){if(l){const t=l[e]-h[e];n[r]=0!==t?(l[i]-h[i])/t:0}o[r]=a?l?ee(n[r-1])!==ee(n[r])?0:(n[r-1]+n[r])/2:n[r-1]:n[r]}!function(t,e,i){const s=t.length;let n,o,r,a,h,l=Si(t,0);for(let c=0;c<s-1;++c)h=l,l=Si(t,c+1),h&&l&&(ie(e[c],0,Mi)?i[c]=i[c+1]=0:(n=i[c]/e[c],o=i[c+1]/e[c],a=Math.pow(n,2)+Math.pow(o,2),a<=9||(r=3/Math.sqrt(a),i[c]=n*r*e[c],i[c+1]=o*r*e[c])))}(t,n,o),function(t,e,i="x"){const s=Oi(i),n=t.length;let o,r,a,h=Si(t,0);for(let l=0;l<n;++l){if(r=a,a=h,h=Si(t,l+1),!a)continue;const n=a[i],c=a[s];r&&(o=(n-r[i])/3,a[`cp1${i}`]=n-o,a[`cp1${s}`]=c-o*e[l]),h&&(o=(h[i]-n)/3,a[`cp2${i}`]=n+o,a[`cp2${s}`]=c+o*e[l])}}(t,o,e)}function Ti(t,e,i){return Math.max(Math.min(t,i),e)}function Ci(t,e,i,s,n){let o,r,a,h;if(e.spanGaps&&(t=t.filter((t=>!t.skip))),"monotone"===e.cubicInterpolationMode)Pi(t,n);else{let i=s?t[t.length-1]:t[0];for(o=0,r=t.length;o<r;++o)a=t[o],h=Di(i,a,t[Math.min(o+1,r-(s?0:1))%r],e.tension),a.cp1x=h.previous.x,a.cp1y=h.previous.y,a.cp2x=h.next.x,a.cp2y=h.next.y,i=a}e.capBezierPoints&&function(t,e){let i,s,n,o,r,a=Ue(t[0],e);for(i=0,s=t.length;i<s;++i)r=o,o=a,a=i<s-1&&Ue(t[i+1],e),o&&(n=t[i],r&&(n.cp1x=Ti(n.cp1x,e.left,e.right),n.cp1y=Ti(n.cp1y,e.top,e.bottom)),a&&(n.cp2x=Ti(n.cp2x,e.left,e.right),n.cp2y=Ti(n.cp2y,e.top,e.bottom)))}(t,i)}function Ai(){return"undefined"!=typeof window&&"undefined"!=typeof document}function Ii(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function Li(t,e,i){let s;return"string"==typeof t?(s=parseInt(t,10),-1!==t.indexOf("%")&&(s=s/100*e.parentNode[i])):s=t,s}const Ei=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);const zi=["top","right","bottom","left"];function Ri(t,e,i){const s={};i=i?"-"+i:"";for(let n=0;n<4;n++){const o=zi[n];s[o]=parseFloat(t[e+"-"+o+i])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}function Fi(t,e){if("native"in t)return t;const{canvas:i,currentDevicePixelRatio:s}=e,n=Ei(i),o="border-box"===n.boxSizing,r=Ri(n,"padding"),a=Ri(n,"border","width"),{x:h,y:l,box:c}=function(t,e){const i=t.touches,s=i&&i.length?i[0]:t,{offsetX:n,offsetY:o}=s;let r,a,h=!1;if(((t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot))(n,o,t.target))r=n,a=o;else{const t=e.getBoundingClientRect();r=s.clientX-t.left,a=s.clientY-t.top,h=!0}return{x:r,y:a,box:h}}(t,i),d=r.left+(c&&a.left),u=r.top+(c&&a.top);let{width:f,height:g}=e;return o&&(f-=r.width+a.width,g-=r.height+a.height),{x:Math.round((h-d)/f*i.width/s),y:Math.round((l-u)/g*i.height/s)}}const Bi=t=>Math.round(10*t)/10;function Hi(t,e,i,s){const n=Ei(t),o=Ri(n,"margin"),r=Li(n.maxWidth,t,"clientWidth")||Kt,a=Li(n.maxHeight,t,"clientHeight")||Kt,h=function(t,e,i){let s,n;if(void 0===e||void 0===i){const o=t&&Ii(t);if(o){const t=o.getBoundingClientRect(),r=Ei(o),a=Ri(r,"border","width"),h=Ri(r,"padding");e=t.width-h.width-a.width,i=t.height-h.height-a.height,s=Li(r.maxWidth,o,"clientWidth"),n=Li(r.maxHeight,o,"clientHeight")}else e=t.clientWidth,i=t.clientHeight}return{width:e,height:i,maxWidth:s||Kt,maxHeight:n||Kt}}(t,e,i);let{width:l,height:c}=h;if("content-box"===n.boxSizing){const t=Ri(n,"border","width"),e=Ri(n,"padding");l-=e.width+t.width,c-=e.height+t.height}l=Math.max(0,l-o.width),c=Math.max(0,s?l/s:c-o.height),l=Bi(Math.min(l,r,h.maxWidth)),c=Bi(Math.min(c,a,h.maxHeight)),l&&!c&&(c=Bi(l/2));return(void 0!==e||void 0!==i)&&s&&h.height&&c>h.height&&(c=h.height,l=Bi(Math.floor(c*s))),{width:l,height:c}}function Wi(t,e,i){const s=e||1,n=Math.floor(t.height*s),o=Math.floor(t.width*s);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const r=t.canvas;return r.style&&(i||!r.style.height&&!r.style.width)&&(r.style.height=`${t.height}px`,r.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==s||r.height!==n||r.width!==o)&&(t.currentDevicePixelRatio=s,r.height=n,r.width=o,t.ctx.setTransform(s,0,0,s,0,0),!0)}const ji=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};Ai()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(e){}return t}();function Vi(t,e){const i=function(t,e){return Ei(t).getPropertyValue(e)}(t,e),s=i&&i.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function Ni(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function $i(t,e,i,s){return{x:t.x+i*(e.x-t.x),y:"middle"===s?i<.5?t.y:e.y:"after"===s?i<1?t.y:e.y:i>0?e.y:t.y}}function Yi(t,e,i,s){const n={x:t.cp2x,y:t.cp2y},o={x:e.cp1x,y:e.cp1y},r=Ni(t,n,i),a=Ni(n,o,i),h=Ni(o,e,i),l=Ni(r,a,i),c=Ni(a,h,i);return Ni(l,c,i)}function Ui(t,e,i){return t?function(t,e){return{x:i=>t+t+e-i,setWidth(t){e=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}}(e,i):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function Xi(t,e){let i,s;"ltr"!==e&&"rtl"!==e||(i=t.canvas.style,s=[i.getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=s)}function qi(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function Ki(t){return"angle"===t?{between:ce,compare:he,normalize:le}:{between:ue,compare:(t,e)=>t-e,normalize:t=>t}}function Gi({start:t,end:e,count:i,loop:s,style:n}){return{start:t%i,end:e%i,loop:s&&(e-t+1)%i==0,style:n}}function Zi(t,e,i){if(!i)return[t];const{property:s,start:n,end:o}=i,r=e.length,{compare:a,between:h,normalize:l}=Ki(s),{start:c,end:d,loop:u,style:f}=function(t,e,i){const{property:s,start:n,end:o}=i,{between:r,normalize:a}=Ki(s),h=e.length;let l,c,{start:d,end:u,loop:f}=t;if(f){for(d+=h,u+=h,l=0,c=h;l<c&&r(a(e[d%h][s]),n,o);++l)d--,u--;d%=h,u%=h}return u<d&&(u+=h),{start:d,end:u,loop:f,style:t.style}}(t,e,i),g=[];let p,m,x,b=!1,_=null;const y=()=>b||h(n,x,p)&&0!==a(n,x),v=()=>!b||0===a(o,p)||h(o,x,p);for(let w=c,k=c;w<=d;++w)m=e[w%r],m.skip||(p=l(m[s]),p!==x&&(b=h(p,n,o),null===_&&y()&&(_=0===a(p,n)?w:k),null!==_&&v()&&(g.push(Gi({start:_,end:w,loop:u,count:r,style:f})),_=null),k=w,x=p));return null!==_&&g.push(Gi({start:_,end:d,loop:u,count:r,style:f})),g}function Qi(t,e,i,s){return s&&s.setContext&&i?function(t,e,i,s){const n=t._chart.getContext(),o=Ji(t.options),{_datasetIndex:r,options:{spanGaps:a}}=t,h=i.length,l=[];let c=o,d=e[0].start,u=d;function f(t,e,s,n){const o=a?-1:1;if(t!==e){for(t+=h;i[t%h].skip;)t-=o;for(;i[e%h].skip;)e+=o;t%h!==e%h&&(l.push({start:t%h,end:e%h,loop:s,style:n}),c=n,d=e%h)}}for(const g of e){d=a?d:g.start;let t,e=i[d%h];for(u=d+1;u<=g.end;u++){const o=i[u%h];t=Ji(s.setContext(ci(n,{type:"segment",p0:e,p1:o,p0DataIndex:(u-1)%h,p1DataIndex:u%h,datasetIndex:r}))),ts(t,c)&&f(d,u-1,g.loop,c),e=o,c=t}d<u-1&&f(d,u-1,g.loop,c)}return l}(t,e,i,s):e}function Ji(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function ts(t,e){if(!e)return!1;const i=[],s=function(t,e){return Oe(e)?(i.includes(e)||i.push(e),i.indexOf(e)):e};return JSON.stringify(t,s)!==JSON.stringify(e,s)}class es{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,e,i,s){const n=e.listeners[s],o=e.duration;n.forEach((s=>s({chart:t,initial:e.initial,numSteps:o,currentStep:Math.min(i-e.start,o)})))}_refresh(){this._request||(this._running=!0,this._request=be.call(window,(()=>{this._update(),this._request=null,this._running&&this._refresh()})))}_update(t=Date.now()){let e=0;this._charts.forEach(((i,s)=>{if(!i.running||!i.items.length)return;const n=i.items;let o,r=n.length-1,a=!1;for(;r>=0;--r)o=n[r],o._active?(o._total>i.duration&&(i.duration=o._total),o.tick(t),a=!0):(n[r]=n[n.length-1],n.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),n.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),e+=n.length})),this._lastDate=t,0===e&&(this._running=!1)}_getAnims(t){const e=this._charts;let i=e.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},e.set(t,i)),i}listen(t,e,i){this._getAnims(t).listeners[e].push(i)}add(t,e){e&&e.length&&this._getAnims(t).items.push(...e)}has(t){return this._getAnims(t).items.length>0}start(t){const e=this._charts.get(t);e&&(e.running=!0,e.start=Date.now(),e.duration=e.items.reduce(((t,e)=>Math.max(t,e._duration)),0),this._refresh())}running(t){if(!this._running)return!1;const e=this._charts.get(t);return!!(e&&e.running&&e.items.length)}stop(t){const e=this._charts.get(t);if(!e||!e.items.length)return;const i=e.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();e.items=[],this._notify(t,e,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var is=new es;const ss="transparent",ns={boolean:(t,e,i)=>i>.5?e:t,color(t,e,i){const s=De(t||ss),n=s.valid&&De(e||ss);return n&&n.valid?n.mix(s,i).hexString():e},number:(t,e,i)=>t+(e-t)*i};class os{constructor(t,e,i,s){const n=e[i];s=hi([t.to,s,n,t.from]);const o=hi([t.from,n,s]);this._active=!0,this._fn=t.fn||ns[t.type||typeof o],this._easing=Se[t.easing]||Se.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=e,this._prop=i,this._from=o,this._to=s,this._promises=void 0}active(){return this._active}update(t,e,i){if(this._active){this._notify(!1);const s=this._target[this._prop],n=i-this._start,o=this._duration-n;this._start=i,this._duration=Math.floor(Math.max(o,t.duration)),this._total+=n,this._loop=!!t.loop,this._to=hi([t.to,e,s,t.from]),this._from=hi([t.from,s,e])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const e=t-this._start,i=this._duration,s=this._prop,n=this._from,o=this._loop,r=this._to;let a;if(this._active=n!==r&&(o||e<i),!this._active)return this._target[s]=r,void this._notify(!0);e<0?this._target[s]=n:(a=e/i%2,a=o&&a>1?2-a:a,a=this._easing(Math.min(1,Math.max(0,a))),this._target[s]=this._fn(n,r,a))}wait(){const t=this._promises||(this._promises=[]);return new Promise(((e,i)=>{t.push({res:e,rej:i})}))}_notify(t){const e=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][e]()}}class rs{constructor(t,e){this._chart=t,this._properties=new Map,this.configure(e)}configure(t){if(!Dt(t))return;const e=Object.keys(We.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach((s=>{const n=t[s];if(!Dt(n))return;const o={};for(const t of e)o[t]=n[t];(Ot(n.properties)&&n.properties||[s]).forEach((t=>{t!==s&&i.has(t)||i.set(t,o)}))}))}_animateOptions(t,e){const i=e.options,s=function(t,e){if(!e)return;let i=t.options;if(!i)return void(t.options=e);i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}}));return i}(t,i);if(!s)return[];const n=this._createAnimations(s,i);return i.$shared&&function(t,e){const i=[],s=Object.keys(e);for(let n=0;n<s.length;n++){const e=t[s[n]];e&&e.active()&&i.push(e.wait())}return Promise.all(i)}(t.options.$animations,i).then((()=>{t.options=i}),(()=>{})),n}_createAnimations(t,e){const i=this._properties,s=[],n=t.$animations||(t.$animations={}),o=Object.keys(e),r=Date.now();let a;for(a=o.length-1;a>=0;--a){const h=o[a];if("$"===h.charAt(0))continue;if("options"===h){s.push(...this._animateOptions(t,e));continue}const l=e[h];let c=n[h];const d=i.get(h);if(c){if(d&&c.active()){c.update(d,l,r);continue}c.cancel()}d&&d.duration?(n[h]=c=new os(d,t,h,l),s.push(c)):t[h]=l}return s}update(t,e){if(0===this._properties.size)return void Object.assign(t,e);const i=this._createAnimations(t,e);return i.length?(is.add(this._chart,i),!0):void 0}}function as(t,e){const i=t&&t.options||{},s=i.reverse,n=void 0===i.min?e:0,o=void 0===i.max?e:0;return{start:s?o:n,end:s?n:o}}function hs(t,e){const i=[],s=t._getSortedDatasetMetas(e);let n,o;for(n=0,o=s.length;n<o;++n)i.push(s[n].index);return i}function ls(t,e,i,s={}){const n=t.keys,o="single"===s.mode;let r,a,h,l;if(null===e)return;let c=!1;for(r=0,a=n.length;r<a;++r){if(h=+n[r],h===i){if(c=!0,s.all)continue;break}l=t.values[h],Pt(l)&&(o||0===e||ee(e)===ee(l))&&(e+=l)}return c||s.all?e:0}function cs(t,e){const i=t&&t.options.stacked;return i||void 0===i&&void 0!==e.stack}function ds(t,e,i){const s=t[e]||(t[e]={});return s[i]||(s[i]={})}function us(t,e,i,s){for(const n of e.getMatchingVisibleMetas(s).reverse()){const e=t[n.index];if(i&&e>0||!i&&e<0)return n.index}return null}function fs(t,e){const{chart:i,_cachedMeta:s}=t,n=i._stacks||(i._stacks={}),{iScale:o,vScale:r,index:a}=s,h=o.axis,l=r.axis,c=function(t,e,i){return`${t.id}.${e.id}.${i.stack||i.type}`}(o,r,s),d=e.length;let u;for(let f=0;f<d;++f){const t=e[f],{[h]:i,[l]:o}=t;u=(t._stacks||(t._stacks={}))[l]=ds(n,c,i),u[a]=o,u._top=us(u,r,!0,s.type),u._bottom=us(u,r,!1,s.type);(u._visualValues||(u._visualValues={}))[a]=o}}function gs(t,e){const i=t.scales;return Object.keys(i).filter((t=>i[t].axis===e)).shift()}function ps(t,e){const i=t.controller.index,s=t.vScale&&t.vScale.axis;if(s){e=e||t._parsed;for(const t of e){const e=t._stacks;if(!e||void 0===e[s]||void 0===e[s][i])return;delete e[s][i],void 0!==e[s]._visualValues&&void 0!==e[s]._visualValues[i]&&delete e[s]._visualValues[i]}}}const ms=t=>"reset"===t||"none"===t,xs=(t,e)=>e?t:Object.assign({},t);class bs{constructor(t,e){this.chart=t,this._ctx=t.ctx,this.index=e,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=cs(t.vScale,t),this.addElements(),this.options.fill&&this.chart.isPluginEnabled("filler")}updateIndex(t){this.index!==t&&ps(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,e=this._cachedMeta,i=this.getDataset(),s=(t,e,i,s)=>"x"===t?e:"r"===t?s:i,n=e.xAxisID=Ct(i.xAxisID,gs(t,"x")),o=e.yAxisID=Ct(i.yAxisID,gs(t,"y")),r=e.rAxisID=Ct(i.rAxisID,gs(t,"r")),a=e.indexAxis,h=e.iAxisID=s(a,n,o,r),l=e.vAxisID=s(a,o,n,r);e.xScale=this.getScaleForId(n),e.yScale=this.getScaleForId(o),e.rScale=this.getScaleForId(r),e.iScale=this.getScaleForId(h),e.vScale=this.getScaleForId(l)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const e=this._cachedMeta;return t===e.iScale?e.vScale:e.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&xe(this._data,this),t._stacked&&ps(t)}_dataCheck(){const t=this.getDataset(),e=t.data||(t.data=[]),i=this._data;if(Dt(e)){const t=this._cachedMeta;this._data=function(t,e){const{iScale:i,vScale:s}=e,n="x"===i.axis?"x":"y",o="x"===s.axis?"x":"y",r=Object.keys(t),a=new Array(r.length);let h,l,c;for(h=0,l=r.length;h<l;++h)c=r[h],a[h]={[n]:c,[o]:t[c]};return a}(e,t)}else if(i!==e){if(i){xe(i,this);const t=this._cachedMeta;ps(t),t._parsed=[]}e&&Object.isExtensible(e)&&(n=this,(s=e)._chartjs?s._chartjs.listeners.push(n):(Object.defineProperty(s,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[n]}}),me.forEach((t=>{const e="_onData"+Vt(t),i=s[t];Object.defineProperty(s,t,{configurable:!0,enumerable:!1,value(...t){const n=i.apply(this,t);return s._chartjs.listeners.forEach((i=>{"function"==typeof i[e]&&i[e](...t)})),n}})})))),this._syncList=[],this._data=e}var s,n}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const e=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const n=e._stacked;e._stacked=cs(e.vScale,e),e.stack!==i.stack&&(s=!0,ps(e),e.stack=i.stack),this._resyncElements(t),(s||n!==e._stacked)&&(fs(this,e._parsed),e._stacked=cs(e.vScale,e))}configure(){const t=this.chart.config,e=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),e,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,e){const{_cachedMeta:i,_data:s}=this,{iScale:n,_stacked:o}=i,r=n.axis;let a,h,l,c=0===t&&e===s.length||i._sorted,d=t>0&&i._parsed[t-1];if(!1===this._parsing)i._parsed=s,i._sorted=!0,l=s;else{l=Ot(s[t])?this.parseArrayData(i,s,t,e):Dt(s[t])?this.parseObjectData(i,s,t,e):this.parsePrimitiveData(i,s,t,e);const n=()=>null===h[r]||d&&h[r]<d[r];for(a=0;a<e;++a)i._parsed[a+t]=h=l[a],c&&(n()&&(c=!1),d=h);i._sorted=c}o&&fs(this,l)}parsePrimitiveData(t,e,i,s){const{iScale:n,vScale:o}=t,r=n.axis,a=o.axis,h=n.getLabels(),l=n===o,c=new Array(s);let d,u,f;for(d=0,u=s;d<u;++d)f=d+i,c[d]={[r]:l||n.parse(h[f],f),[a]:o.parse(e[f],f)};return c}parseArrayData(t,e,i,s){const{xScale:n,yScale:o}=t,r=new Array(s);let a,h,l,c;for(a=0,h=s;a<h;++a)l=a+i,c=e[l],r[a]={x:n.parse(c[0],l),y:o.parse(c[1],l)};return r}parseObjectData(t,e,i,s){const{xScale:n,yScale:o}=t,{xAxisKey:r="x",yAxisKey:a="y"}=this._parsing,h=new Array(s);let l,c,d,u;for(l=0,c=s;l<c;++l)d=l+i,u=e[d],h[l]={x:n.parse(jt(u,r),d),y:o.parse(jt(u,a),d)};return h}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,e,i){const s=this.chart,n=this._cachedMeta,o=e[t.axis];return ls({keys:hs(s,!0),values:e._stacks[t.axis]._visualValues},o,n.index,{mode:i})}updateRangeFromParsed(t,e,i,s){const n=i[e.axis];let o=null===n?NaN:n;const r=s&&i._stacks[e.axis];s&&r&&(s.values=r,o=ls(s,n,this._cachedMeta.index)),t.min=Math.min(t.min,o),t.max=Math.max(t.max,o)}getMinMax(t,e){const i=this._cachedMeta,s=i._parsed,n=i._sorted&&t===i.iScale,o=s.length,r=this._getOtherScale(t),a=((t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:hs(i,!0),values:null})(e,i,this.chart),h={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:l,max:c}=function(t){const{min:e,max:i,minDefined:s,maxDefined:n}=t.getUserBounds();return{min:s?e:Number.NEGATIVE_INFINITY,max:n?i:Number.POSITIVE_INFINITY}}(r);let d,u;function f(){u=s[d];const e=u[r.axis];return!Pt(u[t.axis])||l>e||c<e}for(d=0;d<o&&(f()||(this.updateRangeFromParsed(h,t,u,a),!n));++d);if(n)for(d=o-1;d>=0;--d)if(!f()){this.updateRangeFromParsed(h,t,u,a);break}return h}getAllParsedValues(t){const e=this._cachedMeta._parsed,i=[];let s,n,o;for(s=0,n=e.length;s<n;++s)o=e[s][t.axis],Pt(o)&&i.push(o);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const e=this._cachedMeta,i=e.iScale,s=e.vScale,n=this.getParsed(t);return{label:i?""+i.getLabelForValue(n[i.axis]):"",value:s?""+s.getLabelForValue(n[s.axis]):""}}_update(t){const e=this._cachedMeta;this.update(t||"default"),e._clip=function(t){let e,i,s,n;return Dt(t)?(e=t.top,i=t.right,s=t.bottom,n=t.left):e=i=s=n=t,{top:e,right:i,bottom:s,left:n,disabled:!1===t}}(Ct(this.options.clip,function(t,e,i){if(!1===i)return!1;const s=as(t,i),n=as(e,i);return{top:n.end,right:s.end,bottom:n.start,left:s.start}}(e.xScale,e.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,e=this.chart,i=this._cachedMeta,s=i.data||[],n=e.chartArea,o=[],r=this._drawStart||0,a=this._drawCount||s.length-r,h=this.options.drawActiveElementsOnTop;let l;for(i.dataset&&i.dataset.draw(t,n,r,a),l=r;l<r+a;++l){const e=s[l];e.hidden||(e.active&&h?o.push(e):e.draw(t,n))}for(l=0;l<o.length;++l)o[l].draw(t,n)}getStyle(t,e){const i=e?"active":"default";return void 0===t&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,e,i){const s=this.getDataset();let n;if(t>=0&&t<this._cachedMeta.data.length){const e=this._cachedMeta.data[t];n=e.$context||(e.$context=function(t,e,i){return ci(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:i,index:e,mode:"default",type:"data"})}(this.getContext(),t,e)),n.parsed=this.getParsed(t),n.raw=s.data[t],n.index=n.dataIndex=t}else n=this.$context||(this.$context=function(t,e){return ci(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),n.dataset=s,n.index=n.datasetIndex=this.index;return n.active=!!e,n.mode=i,n}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,e){return this._resolveElementOptions(this.dataElementType.id,e,t)}_resolveElementOptions(t,e="default",i){const s="active"===e,n=this._cachedDataOpts,o=t+"-"+e,r=n[o],a=this.enableOptionSharing&&Nt(i);if(r)return xs(r,a);const h=this.chart.config,l=h.datasetElementScopeKeys(this._type,t),c=s?[`${t}Hover`,"hover",t,""]:[t,""],d=h.getOptionScopes(this.getDataset(),l),u=Object.keys(We.elements[t]),f=h.resolveNamedOptions(d,u,(()=>this.getContext(i,s,e)),c);return f.$shared&&(f.$shared=a,n[o]=Object.freeze(xs(f,a))),f}_resolveAnimations(t,e,i){const s=this.chart,n=this._cachedDataOpts,o=`animation-${e}`,r=n[o];if(r)return r;let a;if(!1!==s.options.animation){const s=this.chart.config,n=s.datasetAnimationScopeKeys(this._type,e),o=s.getOptionScopes(this.getDataset(),n);a=s.createResolver(o,this.getContext(t,i,e))}const h=new rs(s,a&&a.animations);return a&&a._cacheable&&(n[o]=Object.freeze(h)),h}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,e){return!e||ms(t)||this.chart._animationsDisabled}_getSharedOptions(t,e){const i=this.resolveDataElementOptions(t,e),s=this._sharedOptions,n=this.getSharedOptions(i),o=this.includeOptions(e,n)||n!==s;return this.updateSharedOptions(n,e,i),{sharedOptions:n,includeOptions:o}}updateElement(t,e,i,s){ms(s)?Object.assign(t,i):this._resolveAnimations(e,s).update(t,i)}updateSharedOptions(t,e,i){t&&!ms(e)&&this._resolveAnimations(void 0,e).update(t,i)}_setStyle(t,e,i,s){t.active=s;const n=this.getStyle(e,s);this._resolveAnimations(e,i,s).update(t,{options:!s&&this.getSharedOptions(n)||n})}removeHoverStyle(t,e,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,e,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const e=this._data,i=this._cachedMeta.data;for(const[r,a,h]of this._syncList)this[r](a,h);this._syncList=[];const s=i.length,n=e.length,o=Math.min(n,s);o&&this.parse(0,o),n>s?this._insertElements(s,n-s,t):n<s&&this._removeElements(n,s-n)}_insertElements(t,e,i=!0){const s=this._cachedMeta,n=s.data,o=t+e;let r;const a=t=>{for(t.length+=e,r=t.length-1;r>=o;r--)t[r]=t[r-e]};for(a(n),r=t;r<o;++r)n[r]=new this.dataElementType;this._parsing&&a(s._parsed),this.parse(t,e),i&&this.updateElements(n,t,e,"reset")}updateElements(t,e,i,s){}_removeElements(t,e){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,e);i._stacked&&ps(i,s)}i.data.splice(t,e)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[e,i,s]=t;this[e](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,e){e&&this._sync(["_removeElements",t,e]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}i(bs,"defaults",{}),i(bs,"datasetElementType",null),i(bs,"dataElementType",null);class _s extends bs{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const e=this._cachedMeta,{dataset:i,data:s=[],_dataset:n}=e,o=this.chart._animationsDisabled;let{start:r,count:a}=function(t,e,i){const s=e.length;let n=0,o=s;if(t._sorted){const{iScale:r,_parsed:a}=t,h=r.axis,{min:l,max:c,minDefined:d,maxDefined:u}=r.getUserBounds();d&&(n=de(Math.min(ge(a,h,l).lo,i?s:ge(e,h,r.getPixelForValue(l)).lo),0,s-1)),o=u?de(Math.max(ge(a,r.axis,c,!0).hi+1,i?0:ge(e,h,r.getPixelForValue(c),!0).hi+1),n,s)-n:s-n}return{start:n,count:o}}(e,s,o);this._drawStart=r,this._drawCount=a,function(t){const{xScale:e,yScale:i,_scaleRanges:s}=t,n={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!s)return t._scaleRanges=n,!0;const o=s.xmin!==e.min||s.xmax!==e.max||s.ymin!==i.min||s.ymax!==i.max;return Object.assign(s,n),o}(e)&&(r=0,a=s.length),i._chart=this.chart,i._datasetIndex=this.index,i._decimated=!!n._decimated,i.points=s;const h=this.resolveDatasetElementOptions(t);this.options.showLine||(h.borderWidth=0),h.segment=this.options.segment,this.updateElement(i,void 0,{animated:!o,options:h},t),this.updateElements(s,r,a,t)}updateElements(t,e,i,s){const n="reset"===s,{iScale:o,vScale:r,_stacked:a,_dataset:h}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(e,s),d=o.axis,u=r.axis,{spanGaps:f,segment:g}=this.options,p=ne(f)?f:Number.POSITIVE_INFINITY,m=this.chart._animationsDisabled||n||"none"===s,x=e+i,b=t.length;let _=e>0&&this.getParsed(e-1);for(let y=0;y<b;++y){const i=t[y],f=m?i:{};if(y<e||y>=x){f.skip=!0;continue}const b=this.getParsed(y),v=St(b[u]),w=f[d]=o.getPixelForValue(b[d],y),k=f[u]=n||v?r.getBasePixel():r.getPixelForValue(a?this.applyStack(r,b,a):b[u],y);f.skip=isNaN(w)||isNaN(k)||v,f.stop=y>0&&Math.abs(b[d]-_[d])>p,g&&(f.parsed=b,f.raw=h.data[y]),c&&(f.options=l||this.resolveDataElementOptions(y,i.active?"active":s)),m||this.updateElement(i,y,f,s),_=b}}getMaxOverflow(){const t=this._cachedMeta,e=t.dataset,i=e.options&&e.options.borderWidth||0,s=t.data||[];if(!s.length)return i;const n=s[0].size(this.resolveDataElementOptions(0)),o=s[s.length-1].size(this.resolveDataElementOptions(s.length-1));return Math.max(i,n,o)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}function ys(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}i(_s,"id","line"),i(_s,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),i(_s,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class vs{constructor(t){i(this,"options"),this.options=t||{}}static override(t){Object.assign(vs.prototype,t)}init(){}formats(){return ys()}parse(){return ys()}format(){return ys()}add(){return ys()}diff(){return ys()}startOf(){return ys()}endOf(){return ys()}}var ws={_date:vs};function ks(t,e,i,s){const{controller:n,data:o,_sorted:r}=t,a=n._cachedMeta.iScale;if(a&&e===a.axis&&"r"!==e&&r&&o.length){const t=a._reversePixels?pe:ge;if(!s)return t(o,e,i);if(n._sharedOptions){const s=o[0],n="function"==typeof s.getRange&&s.getRange(e);if(n){const s=t(o,e,i-n),r=t(o,e,i+n);return{lo:s.lo,hi:r.hi}}}}return{lo:0,hi:o.length-1}}function Ms(t,e,i,s,n){const o=t.getSortedVisibleDatasetMetas(),r=i[e];for(let a=0,h=o.length;a<h;++a){const{index:t,data:i}=o[a],{lo:h,hi:l}=ks(o[a],e,r,n);for(let e=h;e<=l;++e){const n=i[e];n.skip||s(n,t,e)}}}function Ss(t,e,i,s,n){const o=[];if(!n&&!t.isPointInArea(e))return o;return Ms(t,i,e,(function(i,r,a){(n||Ue(i,t.chartArea,0))&&i.inRange(e.x,e.y,s)&&o.push({element:i,datasetIndex:r,index:a})}),!0),o}function Os(t,e,i,s){let n=[];return Ms(t,i,e,(function(t,i,o){const{startAngle:r,endAngle:a}=t.getProps(["startAngle","endAngle"],s),{angle:h}=function(t,e){const i=e.x-t.x,s=e.y-t.y,n=Math.sqrt(i*i+s*s);let o=Math.atan2(s,i);return o<-.5*Ut&&(o+=Xt),{angle:o,distance:n}}(t,{x:e.x,y:e.y});ce(h,r,a)&&n.push({element:t,datasetIndex:i,index:o})})),n}function Ds(t,e,i,s,n,o){let r=[];const a=function(t){const e=-1!==t.indexOf("x"),i=-1!==t.indexOf("y");return function(t,s){const n=e?Math.abs(t.x-s.x):0,o=i?Math.abs(t.y-s.y):0;return Math.sqrt(Math.pow(n,2)+Math.pow(o,2))}}(i);let h=Number.POSITIVE_INFINITY;return Ms(t,i,e,(function(i,l,c){const d=i.inRange(e.x,e.y,n);if(s&&!d)return;const u=i.getCenterPoint(n);if(!(!!o||t.isPointInArea(u))&&!d)return;const f=a(e,u);f<h?(r=[{element:i,datasetIndex:l,index:c}],h=f):f===h&&r.push({element:i,datasetIndex:l,index:c})})),r}function Ps(t,e,i,s,n,o){return o||t.isPointInArea(e)?"r"!==i||s?Ds(t,e,i,s,n,o):Os(t,e,i,n):[]}function Ts(t,e,i,s,n){const o=[],r="x"===i?"inXRange":"inYRange";let a=!1;return Ms(t,i,e,((t,s,h)=>{t[r]&&t[r](e[i],n)&&(o.push({element:t,datasetIndex:s,index:h}),a=a||t.inRange(e.x,e.y,n))})),s&&!a?[]:o}var Cs={evaluateInteractionItems:Ms,modes:{index(t,e,i,s){const n=Fi(e,t),o=i.axis||"x",r=i.includeInvisible||!1,a=i.intersect?Ss(t,n,o,s,r):Ps(t,n,o,!1,s,r),h=[];return a.length?(t.getSortedVisibleDatasetMetas().forEach((t=>{const e=a[0].index,i=t.data[e];i&&!i.skip&&h.push({element:i,datasetIndex:t.index,index:e})})),h):[]},dataset(t,e,i,s){const n=Fi(e,t),o=i.axis||"xy",r=i.includeInvisible||!1;let a=i.intersect?Ss(t,n,o,s,r):Ps(t,n,o,!1,s,r);if(a.length>0){const e=a[0].datasetIndex,i=t.getDatasetMeta(e).data;a=[];for(let t=0;t<i.length;++t)a.push({element:i[t],datasetIndex:e,index:t})}return a},point:(t,e,i,s)=>Ss(t,Fi(e,t),i.axis||"xy",s,i.includeInvisible||!1),nearest(t,e,i,s){const n=Fi(e,t),o=i.axis||"xy",r=i.includeInvisible||!1;return Ps(t,n,o,i.intersect,s,r)},x:(t,e,i,s)=>Ts(t,Fi(e,t),"x",i.intersect,s),y:(t,e,i,s)=>Ts(t,Fi(e,t),"y",i.intersect,s)}};const As=["left","top","right","bottom"];function Is(t,e){return t.filter((t=>t.pos===e))}function Ls(t,e){return t.filter((t=>-1===As.indexOf(t.pos)&&t.box.axis===e))}function Es(t,e){return t.sort(((t,i)=>{const s=e?i:t,n=e?t:i;return s.weight===n.weight?s.index-n.index:s.weight-n.weight}))}function zs(t,e){const i=function(t){const e={};for(const i of t){const{stack:t,pos:s,stackWeight:n}=i;if(!t||!As.includes(s))continue;const o=e[t]||(e[t]={count:0,placed:0,weight:0,size:0});o.count++,o.weight+=n}return e}(t),{vBoxMaxWidth:s,hBoxMaxHeight:n}=e;let o,r,a;for(o=0,r=t.length;o<r;++o){a=t[o];const{fullSize:r}=a.box,h=i[a.stack],l=h&&a.stackWeight/h.weight;a.horizontal?(a.width=l?l*s:r&&e.availableWidth,a.height=n):(a.width=s,a.height=l?l*n:r&&e.availableHeight)}return i}function Rs(t,e,i,s){return Math.max(t[i],e[i])+Math.max(t[s],e[s])}function Fs(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function Bs(t,e,i,s){const{pos:n,box:o}=i,r=t.maxPadding;if(!Dt(n)){i.size&&(t[n]-=i.size);const e=s[i.stack]||{size:0,count:1};e.size=Math.max(e.size,i.horizontal?o.height:o.width),i.size=e.size/e.count,t[n]+=i.size}o.getPadding&&Fs(r,o.getPadding());const a=Math.max(0,e.outerWidth-Rs(r,t,"left","right")),h=Math.max(0,e.outerHeight-Rs(r,t,"top","bottom")),l=a!==t.w,c=h!==t.h;return t.w=a,t.h=h,i.horizontal?{same:l,other:c}:{same:c,other:l}}function Hs(t,e){const i=e.maxPadding;function s(t){const s={left:0,top:0,right:0,bottom:0};return t.forEach((t=>{s[t]=Math.max(e[t],i[t])})),s}return s(t?["left","right"]:["top","bottom"])}function Ws(t,e,i,s){const n=[];let o,r,a,h,l,c;for(o=0,r=t.length,l=0;o<r;++o){a=t[o],h=a.box,h.update(a.width||e.w,a.height||e.h,Hs(a.horizontal,e));const{same:r,other:d}=Bs(e,i,a,s);l|=r&&n.length,c=c||d,h.fullSize||n.push(a)}return l&&Ws(n,e,i,s)||c}function js(t,e,i,s,n){t.top=i,t.left=e,t.right=e+s,t.bottom=i+n,t.width=s,t.height=n}function Vs(t,e,i,s){const n=i.padding;let{x:o,y:r}=e;for(const a of t){const t=a.box,h=s[a.stack]||{count:1,placed:0,weight:1},l=a.stackWeight/h.weight||1;if(a.horizontal){const s=e.w*l,o=h.size||t.height;Nt(h.start)&&(r=h.start),t.fullSize?js(t,n.left,r,i.outerWidth-n.right-n.left,o):js(t,e.left+h.placed,r,s,o),h.start=r,h.placed+=s,r=t.bottom}else{const s=e.h*l,r=h.size||t.width;Nt(h.start)&&(o=h.start),t.fullSize?js(t,o,n.top,r,i.outerHeight-n.bottom-n.top):js(t,o,e.top+h.placed,r,s),h.start=o,h.placed+=s,o=t.right}}e.x=o,e.y=r}var Ns={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(t){e.draw(t)}}]},t.boxes.push(e)},removeBox(t,e){const i=t.boxes?t.boxes.indexOf(e):-1;-1!==i&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,s){if(!t)return;const n=ri(t.options.layout.padding),o=Math.max(e-n.width,0),r=Math.max(i-n.height,0),a=function(t){const e=function(t){const e=[];let i,s,n,o,r,a;for(i=0,s=(t||[]).length;i<s;++i)n=t[i],({position:o,options:{stack:r,stackWeight:a=1}}=n),e.push({index:i,box:n,pos:o,horizontal:n.isHorizontal(),weight:n.weight,stack:r&&o+r,stackWeight:a});return e}(t),i=Es(e.filter((t=>t.box.fullSize)),!0),s=Es(Is(e,"left"),!0),n=Es(Is(e,"right")),o=Es(Is(e,"top"),!0),r=Es(Is(e,"bottom")),a=Ls(e,"x"),h=Ls(e,"y");return{fullSize:i,leftAndTop:s.concat(o),rightAndBottom:n.concat(h).concat(r).concat(a),chartArea:Is(e,"chartArea"),vertical:s.concat(n).concat(h),horizontal:o.concat(r).concat(a)}}(t.boxes),h=a.vertical,l=a.horizontal;It(t.boxes,(t=>{"function"==typeof t.beforeLayout&&t.beforeLayout()}));const c=h.reduce(((t,e)=>e.box.options&&!1===e.box.options.display?t:t+1),0)||1,d=Object.freeze({outerWidth:e,outerHeight:i,padding:n,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/c,hBoxMaxHeight:r/2}),u=Object.assign({},n);Fs(u,ri(s));const f=Object.assign({maxPadding:u,w:o,h:r,x:n.left,y:n.top},n),g=zs(h.concat(l),d);Ws(a.fullSize,f,d,g),Ws(h,f,d,g),Ws(l,f,d,g)&&Ws(h,f,d,g),function(t){const e=t.maxPadding;function i(i){const s=Math.max(e[i]-t[i],0);return t[i]+=s,s}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}(f),Vs(a.leftAndTop,f,d,g),f.x+=f.w,f.y+=f.h,Vs(a.rightAndBottom,f,d,g),t.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},It(a.chartArea,(e=>{const i=e.box;Object.assign(i,t.chartArea),i.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})}))}};class $s{acquireContext(t,e){}releaseContext(t){return!1}addEventListener(t,e,i){}removeEventListener(t,e,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,e,i,s){return e=Math.max(0,e||t.width),i=i||t.height,{width:e,height:Math.max(0,s?Math.floor(e/s):i)}}isAttached(t){return!0}updateConfig(t){}}class Ys extends $s{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const Us="$chartjs",Xs={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},qs=t=>null===t||""===t;const Ks=!!ji&&{passive:!0};function Gs(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,Ks)}function Zs(t,e){for(const i of t)if(i===e||i.contains(e))return!0}function Qs(t,e,i){const s=t.canvas,n=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||Zs(i.addedNodes,s),e=e&&!Zs(i.removedNodes,s);e&&i()}));return n.observe(document,{childList:!0,subtree:!0}),n}function Js(t,e,i){const s=t.canvas,n=new MutationObserver((t=>{let e=!1;for(const i of t)e=e||Zs(i.removedNodes,s),e=e&&!Zs(i.addedNodes,s);e&&i()}));return n.observe(document,{childList:!0,subtree:!0}),n}const tn=new Map;let en=0;function sn(){const t=window.devicePixelRatio;t!==en&&(en=t,tn.forEach(((e,i)=>{i.currentDevicePixelRatio!==t&&e()})))}function nn(t,e,i){const s=t.canvas,n=s&&Ii(s);if(!n)return;const o=_e(((t,e)=>{const s=n.clientWidth;i(t,e),s<n.clientWidth&&i()}),window),r=new ResizeObserver((t=>{const e=t[0],i=e.contentRect.width,s=e.contentRect.height;0===i&&0===s||o(i,s)}));return r.observe(n),function(t,e){tn.size||window.addEventListener("resize",sn),tn.set(t,e)}(t,o),r}function on(t,e,i){i&&i.disconnect(),"resize"===e&&function(t){tn.delete(t),tn.size||window.removeEventListener("resize",sn)}(t)}function rn(t,e,i){const s=t.canvas,n=_e((e=>{null!==t.ctx&&i(function(t,e){const i=Xs[t.type]||t.type,{x:s,y:n}=Fi(t,e);return{type:i,chart:e,native:t,x:void 0!==s?s:null,y:void 0!==n?n:null}}(e,t))}),t);return function(t,e,i){t&&t.addEventListener(e,i,Ks)}(s,e,n),n}class an extends $s{acquireContext(t,e){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(function(t,e){const i=t.style,s=t.getAttribute("height"),n=t.getAttribute("width");if(t[Us]={initial:{height:s,width:n,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",qs(n)){const e=Vi(t,"width");void 0!==e&&(t.width=e)}if(qs(s))if(""===t.style.height)t.height=t.width/(e||2);else{const e=Vi(t,"height");void 0!==e&&(t.height=e)}}(t,e),i):null}releaseContext(t){const e=t.canvas;if(!e[Us])return!1;const i=e[Us].initial;["height","width"].forEach((t=>{const s=i[t];St(s)?e.removeAttribute(t):e.setAttribute(t,s)}));const s=i.style||{};return Object.keys(s).forEach((t=>{e.style[t]=s[t]})),e.width=e.width,delete e[Us],!0}addEventListener(t,e,i){this.removeEventListener(t,e);const s=t.$proxies||(t.$proxies={}),n={attach:Qs,detach:Js,resize:nn}[e]||rn;s[e]=n(t,e,i)}removeEventListener(t,e){const i=t.$proxies||(t.$proxies={}),s=i[e];if(!s)return;({attach:on,detach:on,resize:on}[e]||Gs)(t,e,s),i[e]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,e,i,s){return Hi(t,e,i,s)}isAttached(t){const e=t&&Ii(t);return!(!e||!e.isConnected)}}class hn{constructor(){i(this,"x"),i(this,"y"),i(this,"active",!1),i(this,"options"),i(this,"$animations")}tooltipPosition(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}hasValue(){return ne(this.x)&&ne(this.y)}getProps(t,e){const i=this.$animations;if(!e||!i)return this;const s={};return t.forEach((t=>{s[t]=i[t]&&i[t].active()?i[t]._to:this[t]})),s}}function ln(t,e){const i=t.options.ticks,s=function(t){const e=t.options.offset,i=t._tickSize(),s=t._length/i+(e?0:1),n=t._maxLength/i;return Math.floor(Math.min(s,n))}(t),n=Math.min(i.maxTicksLimit||s,s),o=i.major.enabled?function(t){const e=[];let i,s;for(i=0,s=t.length;i<s;i++)t[i].major&&e.push(i);return e}(e):[],r=o.length,a=o[0],h=o[r-1],l=[];if(r>n)return function(t,e,i,s){let n,o=0,r=i[0];for(s=Math.ceil(s),n=0;n<t.length;n++)n===r&&(e.push(t[n]),o++,r=i[o*s])}(e,l,o,r/n),l;const c=function(t,e,i){const s=function(t){const e=t.length;let i,s;if(e<2)return!1;for(s=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==s)return!1;return s}(t),n=e.length/i;if(!s)return Math.max(n,1);const o=function(t){const e=[],i=Math.sqrt(t);let s;for(s=1;s<i;s++)t%s===0&&(e.push(s),e.push(t/s));return i===(0|i)&&e.push(i),e.sort(((t,e)=>t-e)).pop(),e}(s);for(let r=0,a=o.length-1;r<a;r++){const t=o[r];if(t>n)return t}return Math.max(n,1)}(o,e,n);if(r>0){let t,i;const s=r>1?Math.round((h-a)/(r-1)):null;for(cn(e,l,c,St(s)?0:a-s,a),t=0,i=r-1;t<i;t++)cn(e,l,c,o[t],o[t+1]);return cn(e,l,c,h,St(s)?e.length:h+s),l}return cn(e,l,c),l}function cn(t,e,i,s,n){const o=Ct(s,0),r=Math.min(Ct(n,t.length),t.length);let a,h,l,c=0;for(i=Math.ceil(i),n&&(a=n-s,i=a/Math.floor(a/i)),l=o;l<0;)c++,l=Math.round(o+c*i);for(h=Math.max(o,0);h<r;h++)h===l&&(e.push(t[h]),c++,l=Math.round(o+c*i))}i(hn,"defaults",{}),i(hn,"defaultRoutes");const dn=(t,e,i)=>"top"===e||"left"===e?t[e]+i:t[e]-i,un=(t,e)=>Math.min(e||t,t);function fn(t,e){const i=[],s=t.length/e,n=t.length;let o=0;for(;o<n;o+=s)i.push(t[Math.floor(o)]);return i}function gn(t,e,i){const s=t.ticks.length,n=Math.min(e,s-1),o=t._startPixel,r=t._endPixel,a=1e-6;let h,l=t.getPixelForTick(n);if(!(i&&(h=1===s?Math.max(l-o,r-l):0===e?(t.getPixelForTick(1)-l)/2:(l-t.getPixelForTick(n-1))/2,l+=n<e?h:-h,l<o-a||l>r+a)))return l}function pn(t){return t.drawTicks?t.tickLength:0}function mn(t,e){if(!t.display)return 0;const i=ai(t.font,e),s=ri(t.padding);return(Ot(t.text)?t.text.length:1)*i.lineHeight+s.height}function xn(t,e,i){let s=ye(t);return(i&&"right"!==e||!i&&"right"===e)&&(s=(t=>"left"===t?"right":"right"===t?"left":t)(s)),s}class bn extends hn{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,e){return t}getUserBounds(){let{_userMin:t,_userMax:e,_suggestedMin:i,_suggestedMax:s}=this;return t=Tt(t,Number.POSITIVE_INFINITY),e=Tt(e,Number.NEGATIVE_INFINITY),i=Tt(i,Number.POSITIVE_INFINITY),s=Tt(s,Number.NEGATIVE_INFINITY),{min:Tt(t,i),max:Tt(e,s),minDefined:Pt(t),maxDefined:Pt(e)}}getMinMax(t){let e,{min:i,max:s,minDefined:n,maxDefined:o}=this.getUserBounds();if(n&&o)return{min:i,max:s};const r=this.getMatchingVisibleMetas();for(let a=0,h=r.length;a<h;++a)e=r[a].controller.getMinMax(this,t),n||(i=Math.min(i,e.min)),o||(s=Math.max(s,e.max));return i=o&&i>s?s:i,s=n&&i>s?i:s,{min:Tt(i,Tt(s,i)),max:Tt(s,Tt(i,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){At(this.options.beforeUpdate,[this])}update(t,e,i){const{beginAtZero:s,grace:n,ticks:o}=this.options,r=o.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=e,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=li(this,n,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const a=r<this.ticks.length;this._convertTicksToLabels(a?fn(this.ticks,r):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),o.display&&(o.autoSkip||"auto"===o.source)&&(this.ticks=ln(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),a&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t,e,i=this.options.reverse;this.isHorizontal()?(t=this.left,e=this.right):(t=this.top,e=this.bottom,i=!i),this._startPixel=t,this._endPixel=e,this._reversePixels=i,this._length=e-t,this._alignToPixels=this.options.alignToPixels}afterUpdate(){At(this.options.afterUpdate,[this])}beforeSetDimensions(){At(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){At(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),At(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){At(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const e=this.options.ticks;let i,s,n;for(i=0,s=t.length;i<s;i++)n=t[i],n.label=At(e.callback,[n.value,i,t],this)}afterTickToLabelConversion(){At(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){At(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,e=t.ticks,i=un(this.ticks.length,t.ticks.maxTicksLimit),s=e.minRotation||0,n=e.maxRotation;let o,r,a,h=s;if(!this._isVisible()||!e.display||s>=n||i<=1||!this.isHorizontal())return void(this.labelRotation=s);const l=this._getLabelSizes(),c=l.widest.width,d=l.highest.height,u=de(this.chart.width-c,0,this.maxWidth);o=t.offset?this.maxWidth/i:u/(i-1),c+6>o&&(o=u/(i-(t.offset?.5:1)),r=this.maxHeight-pn(t.grid)-e.padding-mn(t.title,this.chart.options.font),a=Math.sqrt(c*c+d*d),h=Math.min(Math.asin(de((l.highest.height+6)/o,-1,1)),Math.asin(de(r/a,-1,1))-Math.asin(de(d/a,-1,1)))*(180/Ut),h=Math.max(s,Math.min(n,h))),this.labelRotation=h}afterCalculateLabelRotation(){At(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){At(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:e,options:{ticks:i,title:s,grid:n}}=this,o=this._isVisible(),r=this.isHorizontal();if(o){const o=mn(s,e.options.font);if(r?(t.width=this.maxWidth,t.height=pn(n)+o):(t.height=this.maxHeight,t.width=pn(n)+o),i.display&&this.ticks.length){const{first:e,last:s,widest:n,highest:o}=this._getLabelSizes(),a=2*i.padding,h=oe(this.labelRotation),l=Math.cos(h),c=Math.sin(h);if(r){const e=i.mirror?0:c*n.width+l*o.height;t.height=Math.min(this.maxHeight,t.height+e+a)}else{const e=i.mirror?0:l*n.width+c*o.height;t.width=Math.min(this.maxWidth,t.width+e+a)}this._calculatePadding(e,s,c,l)}}this._handleMargins(),r?(this.width=this._length=e.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=e.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,e,i,s){const{ticks:{align:n,padding:o},position:r}=this.options,a=0!==this.labelRotation,h="top"!==r&&"x"===this.axis;if(this.isHorizontal()){const r=this.getPixelForTick(0)-this.left,l=this.right-this.getPixelForTick(this.ticks.length-1);let c=0,d=0;a?h?(c=s*t.width,d=i*e.height):(c=i*t.height,d=s*e.width):"start"===n?d=e.width:"end"===n?c=t.width:"inner"!==n&&(c=t.width/2,d=e.width/2),this.paddingLeft=Math.max((c-r+o)*this.width/(this.width-r),0),this.paddingRight=Math.max((d-l+o)*this.width/(this.width-l),0)}else{let i=e.height/2,s=t.height/2;"start"===n?(i=0,s=t.height):"end"===n&&(i=e.height,s=0),this.paddingTop=i+o,this.paddingBottom=s+o}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){At(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:e}=this.options;return"top"===e||"bottom"===e||"x"===t}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){let e,i;for(this.beforeTickToLabelConversion(),this.generateTickLabels(t),e=0,i=t.length;e<i;e++)St(t[e].label)&&(t.splice(e,1),i--,e--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const e=this.options.ticks.sampleSize;let i=this.ticks;e<i.length&&(i=fn(i,e)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,e,i){const{ctx:s,_longestTextCache:n}=this,o=[],r=[],a=Math.floor(e/un(e,i));let h,l,c,d,u,f,g,p,m,x,b,_=0,y=0;for(h=0;h<e;h+=a){if(d=t[h].label,u=this._resolveTickFontOptions(h),s.font=f=u.string,g=n[f]=n[f]||{data:{},gc:[]},p=u.lineHeight,m=x=0,St(d)||Ot(d)){if(Ot(d))for(l=0,c=d.length;l<c;++l)b=d[l],St(b)||Ot(b)||(m=je(s,g.data,g.gc,m,b),x+=p)}else m=je(s,g.data,g.gc,m,d),x=p;o.push(m),r.push(x),_=Math.max(m,_),y=Math.max(x,y)}!function(t,e){It(t,(t=>{const i=t.gc,s=i.length/2;let n;if(s>e){for(n=0;n<s;++n)delete t.data[i[n]];i.splice(0,s)}}))}(n,e);const v=o.indexOf(_),w=r.indexOf(y),k=t=>({width:o[t]||0,height:r[t]||0});return{first:k(0),last:k(e-1),widest:k(v),highest:k(w),widths:o,heights:r}}getLabelForValue(t){return t}getPixelForValue(t,e){return NaN}getValueForPixel(t){}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const e=this._startPixel+t*this._length;return de(this._alignToPixels?Ve(this.chart,e,0):e,-32768,32767)}getDecimalForPixel(t){const e=(t-this._startPixel)/this._length;return this._reversePixels?1-e:e}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:e}=this;return t<0&&e<0?e:t>0&&e>0?t:0}getContext(t){const e=this.ticks||[];if(t>=0&&t<e.length){const i=e[t];return i.$context||(i.$context=function(t,e,i){return ci(t,{tick:i,index:e,type:"tick"})}(this.getContext(),t,i))}return this.$context||(this.$context=ci(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){const t=this.options.ticks,e=oe(this.labelRotation),i=Math.abs(Math.cos(e)),s=Math.abs(Math.sin(e)),n=this._getLabelSizes(),o=t.autoSkipPadding||0,r=n?n.widest.width+o:0,a=n?n.highest.height+o:0;return this.isHorizontal()?a*i>r*s?r/i:a/s:a*s<r*i?a/i:r/s}_isVisible(){const t=this.options.display;return"auto"!==t?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const e=this.axis,i=this.chart,s=this.options,{grid:n,position:o,border:r}=s,a=n.offset,h=this.isHorizontal(),l=this.ticks.length+(a?1:0),c=pn(n),d=[],u=r.setContext(this.getContext()),f=u.display?u.width:0,g=f/2,p=function(t){return Ve(i,t,f)};let m,x,b,_,y,v,w,k,M,S,O,D;if("top"===o)m=p(this.bottom),v=this.bottom-c,k=m-g,S=p(t.top)+g,D=t.bottom;else if("bottom"===o)m=p(this.top),S=t.top,D=p(t.bottom)-g,v=m+g,k=this.top+c;else if("left"===o)m=p(this.right),y=this.right-c,w=m-g,M=p(t.left)+g,O=t.right;else if("right"===o)m=p(this.left),M=t.left,O=p(t.right)-g,y=m+g,w=this.left+c;else if("x"===e){if("center"===o)m=p((t.top+t.bottom)/2+.5);else if(Dt(o)){const t=Object.keys(o)[0],e=o[t];m=p(this.chart.scales[t].getPixelForValue(e))}S=t.top,D=t.bottom,v=m+g,k=v+c}else if("y"===e){if("center"===o)m=p((t.left+t.right)/2);else if(Dt(o)){const t=Object.keys(o)[0],e=o[t];m=p(this.chart.scales[t].getPixelForValue(e))}y=m-g,w=y-c,M=t.left,O=t.right}const P=Ct(s.ticks.maxTicksLimit,l),T=Math.max(1,Math.ceil(l/P));for(x=0;x<l;x+=T){const t=this.getContext(x),e=n.setContext(t),s=r.setContext(t),o=e.lineWidth,l=e.color,c=s.dash||[],u=s.dashOffset,f=e.tickWidth,g=e.tickColor,p=e.tickBorderDash||[],m=e.tickBorderDashOffset;b=gn(this,x,a),void 0!==b&&(_=Ve(i,b,o),h?y=w=M=O=_:v=k=S=D=_,d.push({tx1:y,ty1:v,tx2:w,ty2:k,x1:M,y1:S,x2:O,y2:D,width:o,color:l,borderDash:c,borderDashOffset:u,tickWidth:f,tickColor:g,tickBorderDash:p,tickBorderDashOffset:m}))}return this._ticksLength=l,this._borderValue=m,d}_computeLabelItems(t){const e=this.axis,i=this.options,{position:s,ticks:n}=i,o=this.isHorizontal(),r=this.ticks,{align:a,crossAlign:h,padding:l,mirror:c}=n,d=pn(i.grid),u=d+l,f=c?-l:u,g=-oe(this.labelRotation),p=[];let m,x,b,_,y,v,w,k,M,S,O,D,P="middle";if("top"===s)v=this.bottom-f,w=this._getXAxisLabelAlignment();else if("bottom"===s)v=this.top+f,w=this._getXAxisLabelAlignment();else if("left"===s){const t=this._getYAxisLabelAlignment(d);w=t.textAlign,y=t.x}else if("right"===s){const t=this._getYAxisLabelAlignment(d);w=t.textAlign,y=t.x}else if("x"===e){if("center"===s)v=(t.top+t.bottom)/2+u;else if(Dt(s)){const t=Object.keys(s)[0],e=s[t];v=this.chart.scales[t].getPixelForValue(e)+u}w=this._getXAxisLabelAlignment()}else if("y"===e){if("center"===s)y=(t.left+t.right)/2-u;else if(Dt(s)){const t=Object.keys(s)[0],e=s[t];y=this.chart.scales[t].getPixelForValue(e)}w=this._getYAxisLabelAlignment(d).textAlign}"y"===e&&("start"===a?P="top":"end"===a&&(P="bottom"));const T=this._getLabelSizes();for(m=0,x=r.length;m<x;++m){b=r[m],_=b.label;const t=n.setContext(this.getContext(m));k=this.getPixelForTick(m)+n.labelOffset,M=this._resolveTickFontOptions(m),S=M.lineHeight,O=Ot(_)?_.length:1;const e=O/2,i=t.color,a=t.textStrokeColor,l=t.textStrokeWidth;let d,u=w;if(o?(y=k,"inner"===w&&(u=m===x-1?this.options.reverse?"left":"right":0===m?this.options.reverse?"right":"left":"center"),D="top"===s?"near"===h||0!==g?-O*S+S/2:"center"===h?-T.highest.height/2-e*S+S:-T.highest.height+S/2:"near"===h||0!==g?S/2:"center"===h?T.highest.height/2-e*S:T.highest.height-O*S,c&&(D*=-1),0===g||t.showLabelBackdrop||(y+=S/2*Math.sin(g))):(v=k,D=(1-O)*S/2),t.showLabelBackdrop){const e=ri(t.backdropPadding),i=T.heights[m],s=T.widths[m];let n=D-e.top,o=0-e.left;switch(P){case"middle":n-=i/2;break;case"bottom":n-=i}switch(w){case"center":o-=s/2;break;case"right":o-=s;break;case"inner":m===x-1?o-=s:m>0&&(o-=s/2)}d={left:o,top:n,width:s+e.width,height:i+e.height,color:t.backdropColor}}p.push({label:_,font:M,textOffset:D,options:{rotation:g,color:i,strokeColor:a,strokeWidth:l,textAlign:u,textBaseline:P,translation:[y,v],backdrop:d}})}return p}_getXAxisLabelAlignment(){const{position:t,ticks:e}=this.options;if(-oe(this.labelRotation))return"top"===t?"left":"right";let i="center";return"start"===e.align?i="left":"end"===e.align?i="right":"inner"===e.align&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:e,ticks:{crossAlign:i,mirror:s,padding:n}}=this.options,o=t+n,r=this._getLabelSizes().widest.width;let a,h;return"left"===e?s?(h=this.right+n,"near"===i?a="left":"center"===i?(a="center",h+=r/2):(a="right",h+=r)):(h=this.right-o,"near"===i?a="right":"center"===i?(a="center",h-=r/2):(a="left",h=this.left)):"right"===e?s?(h=this.left+n,"near"===i?a="right":"center"===i?(a="center",h-=r/2):(a="left",h-=r)):(h=this.left+o,"near"===i?a="left":"center"===i?(a="center",h+=r/2):(a="right",h=this.right)):a="right",{textAlign:a,x:h}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,e=this.options.position;return"left"===e||"right"===e?{top:0,left:this.left,bottom:t.height,right:this.right}:"top"===e||"bottom"===e?{top:this.top,left:0,bottom:this.bottom,right:t.width}:void 0}drawBackground(){const{ctx:t,options:{backgroundColor:e},left:i,top:s,width:n,height:o}=this;e&&(t.save(),t.fillStyle=e,t.fillRect(i,s,n,o),t.restore())}getLineWidthForValue(t){const e=this.options.grid;if(!this._isVisible()||!e.display)return 0;const i=this.ticks.findIndex((e=>e.value===t));if(i>=0){return e.setContext(this.getContext(i)).lineWidth}return 0}drawGrid(t){const e=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let n,o;const r=(t,e,s)=>{s.width&&s.color&&(i.save(),i.lineWidth=s.width,i.strokeStyle=s.color,i.setLineDash(s.borderDash||[]),i.lineDashOffset=s.borderDashOffset,i.beginPath(),i.moveTo(t.x,t.y),i.lineTo(e.x,e.y),i.stroke(),i.restore())};if(e.display)for(n=0,o=s.length;n<o;++n){const t=s[n];e.drawOnChartArea&&r({x:t.x1,y:t.y1},{x:t.x2,y:t.y2},t),e.drawTicks&&r({x:t.tx1,y:t.ty1},{x:t.tx2,y:t.ty2},{color:t.tickColor,width:t.tickWidth,borderDash:t.tickBorderDash,borderDashOffset:t.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:e,options:{border:i,grid:s}}=this,n=i.setContext(this.getContext()),o=i.display?n.width:0;if(!o)return;const r=s.setContext(this.getContext(0)).lineWidth,a=this._borderValue;let h,l,c,d;this.isHorizontal()?(h=Ve(t,this.left,o)-o/2,l=Ve(t,this.right,r)+r/2,c=d=a):(c=Ve(t,this.top,o)-o/2,d=Ve(t,this.bottom,r)+r/2,h=l=a),e.save(),e.lineWidth=n.width,e.strokeStyle=n.color,e.beginPath(),e.moveTo(h,c),e.lineTo(l,d),e.stroke(),e.restore()}drawLabels(t){if(!this.options.ticks.display)return;const e=this.ctx,i=this._computeLabelArea();i&&Xe(e,i);const s=this.getLabelItems(t);for(const n of s){const t=n.options,i=n.font;Je(e,n.label,0,n.textOffset,i,t)}i&&qe(e)}drawTitle(){const{ctx:t,options:{position:e,title:i,reverse:s}}=this;if(!i.display)return;const n=ai(i.font),o=ri(i.padding),r=i.align;let a=n.lineHeight/2;"bottom"===e||"center"===e||Dt(e)?(a+=o.bottom,Ot(i.text)&&(a+=n.lineHeight*(i.text.length-1))):a+=o.top;const{titleX:h,titleY:l,maxWidth:c,rotation:d}=function(t,e,i,s){const{top:n,left:o,bottom:r,right:a,chart:h}=t,{chartArea:l,scales:c}=h;let d,u,f,g=0;const p=r-n,m=a-o;if(t.isHorizontal()){if(u=ve(s,o,a),Dt(i)){const t=Object.keys(i)[0],s=i[t];f=c[t].getPixelForValue(s)+p-e}else f="center"===i?(l.bottom+l.top)/2+p-e:dn(t,i,e);d=a-o}else{if(Dt(i)){const t=Object.keys(i)[0],s=i[t];u=c[t].getPixelForValue(s)-m+e}else u="center"===i?(l.left+l.right)/2-m+e:dn(t,i,e);f=ve(s,r,n),g="left"===i?-Zt:Zt}return{titleX:u,titleY:f,maxWidth:d,rotation:g}}(this,a,e,r);Je(t,i.text,0,0,n,{color:i.color,maxWidth:c,rotation:d,textAlign:xn(r,e,s),textBaseline:"middle",translation:[h,l]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,e=t.ticks&&t.ticks.z||0,i=Ct(t.grid&&t.grid.z,-1),s=Ct(t.border&&t.border.z,0);return this._isVisible()&&this.draw===bn.prototype.draw?[{z:i,draw:t=>{this.drawBackground(),this.drawGrid(t),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:e,draw:t=>{this.drawLabels(t)}}]:[{z:e,draw:t=>{this.draw(t)}}]}getMatchingVisibleMetas(t){const e=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let n,o;for(n=0,o=e.length;n<o;++n){const o=e[n];o[i]!==this.id||t&&o.type!==t||s.push(o)}return s}_resolveTickFontOptions(t){return ai(this.options.ticks.setContext(this.getContext(t)).font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class _n{constructor(t,e,i){this.type=t,this.scope=e,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const e=Object.getPrototypeOf(t);let i;(function(t){return"id"in t&&"defaults"in t})(e)&&(i=this.register(e));const s=this.items,n=t.id,o=this.scope+"."+n;if(!n)throw new Error("class does not have id: "+t);return n in s||(s[n]=t,function(t,e,i){const s=Ft(Object.create(null),[i?We.get(i):{},We.get(e),t.defaults]);We.set(e,s),t.defaultRoutes&&function(t,e){Object.keys(e).forEach((i=>{const s=i.split("."),n=s.pop(),o=[t].concat(s).join("."),r=e[i].split("."),a=r.pop(),h=r.join(".");We.route(o,n,h,a)}))}(e,t.defaultRoutes);t.descriptors&&We.describe(e,t.descriptors)}(t,o,i),this.override&&We.override(t.id,t.overrides)),o}get(t){return this.items[t]}unregister(t){const e=this.items,i=t.id,s=this.scope;i in e&&delete e[i],s&&i in We[s]&&(delete We[s][i],this.override&&delete ze[i])}}class yn{constructor(){this.controllers=new _n(bs,"datasets",!0),this.elements=new _n(hn,"elements"),this.plugins=new _n(Object,"plugins"),this.scales=new _n(bn,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,e,i){[...e].forEach((e=>{const s=i||this._getRegistryForType(e);i||s.isForType(e)||s===this.plugins&&e.id?this._exec(t,s,e):It(e,(e=>{const s=i||this._getRegistryForType(e);this._exec(t,s,e)}))}))}_exec(t,e,i){const s=Vt(t);At(i["before"+s],[],i),e[t](i),At(i["after"+s],[],i)}_getRegistryForType(t){for(let e=0;e<this._typedRegistries.length;e++){const i=this._typedRegistries[e];if(i.isForType(t))return i}return this.plugins}_get(t,e,i){const s=e.get(t);if(void 0===s)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var vn=new yn;class wn{constructor(){this._init=[]}notify(t,e,i,s){"beforeInit"===e&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const n=s?this._descriptors(t).filter(s):this._descriptors(t),o=this._notify(n,t,e,i);return"afterDestroy"===e&&(this._notify(n,t,"stop"),this._notify(this._init,t,"uninstall")),o}_notify(t,e,i,s){s=s||{};for(const n of t){const t=n.plugin;if(!1===At(t[i],[e,s,n.options],t)&&s.cancelable)return!1}return!0}invalidate(){St(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const e=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),e}_createDescriptors(t,e){const i=t&&t.config,s=Ct(i.options&&i.options.plugins,{}),n=function(t){const e={},i=[],s=Object.keys(vn.plugins.items);for(let o=0;o<s.length;o++)i.push(vn.getPlugin(s[o]));const n=t.plugins||[];for(let o=0;o<n.length;o++){const t=n[o];-1===i.indexOf(t)&&(i.push(t),e[t.id]=!0)}return{plugins:i,localIds:e}}(i);return!1!==s||e?function(t,{plugins:e,localIds:i},s,n){const o=[],r=t.getContext();for(const a of e){const e=a.id,h=kn(s[e],n);null!==h&&o.push({plugin:a,options:Mn(t.config,{plugin:a,local:i[e]},h,r)})}return o}(t,n,s,e):[]}_notifyStateChanges(t){const e=this._oldCache||[],i=this._cache,s=(t,e)=>t.filter((t=>!e.some((e=>t.plugin.id===e.plugin.id))));this._notify(s(e,i),t,"stop"),this._notify(s(i,e),t,"start")}}function kn(t,e){return e||!1!==t?!0===t?{}:t:null}function Mn(t,{plugin:e,local:i},s,n){const o=t.pluginScopeKeys(e),r=t.getOptionScopes(s,o);return i&&e.defaults&&r.push(e.defaults),t.createResolver(r,n,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Sn(t,e){const i=We.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function On(t){if("x"===t||"y"===t||"r"===t)return t}function Dn(t,...e){if(On(t))return t;for(const s of e){const e=s.axis||("top"===(i=s.position)||"bottom"===i?"x":"left"===i||"right"===i?"y":void 0)||t.length>1&&On(t[0].toLowerCase());if(e)return e}var i;throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function Pn(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function Tn(t,e){const i=ze[t.type]||{scales:{}},s=e.scales||{},n=Sn(t.type,e),o=Object.create(null);return Object.keys(s).forEach((e=>{const r=s[e];if(!Dt(r))return;if(r._proxy)return;const a=Dn(e,r,function(t,e){if(e.data&&e.data.datasets){const i=e.data.datasets.filter((e=>e.xAxisID===t||e.yAxisID===t));if(i.length)return Pn(t,"x",i[0])||Pn(t,"y",i[0])}return{}}(e,t),We.scales[r.type]),h=function(t,e){return t===e?"_index_":"_value_"}(a,n),l=i.scales||{};o[e]=Bt(Object.create(null),[{axis:a},r,l[a],l[h]])})),t.data.datasets.forEach((i=>{const n=i.type||t.type,r=i.indexAxis||Sn(n,e),a=(ze[n]||{}).scales||{};Object.keys(a).forEach((t=>{const e=function(t,e){let i=t;return"_index_"===t?i=e:"_value_"===t&&(i="x"===e?"y":"x"),i}(t,r),n=i[e+"AxisID"]||e;o[n]=o[n]||Object.create(null),Bt(o[n],[{axis:e},s[n],a[t]])}))})),Object.keys(o).forEach((t=>{const e=o[t];Bt(e,[We.scales[e.type],We.scale])})),o}function Cn(t){const e=t.options||(t.options={});e.plugins=Ct(e.plugins,{}),e.scales=Tn(t,e)}function An(t){return(t=t||{}).datasets=t.datasets||[],t.labels=t.labels||[],t}const In=new Map,Ln=new Set;function En(t,e){let i=In.get(t);return i||(i=e(),In.set(t,i),Ln.add(i)),i}const zn=(t,e,i)=>{const s=jt(e,i);void 0!==s&&t.add(s)};class Rn{constructor(t){this._config=function(t){return(t=t||{}).data=An(t.data),Cn(t),t}(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=An(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Cn(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return En(t,(()=>[[`datasets.${t}`,""]]))}datasetAnimationScopeKeys(t,e){return En(`${t}.transition.${e}`,(()=>[[`datasets.${t}.transitions.${e}`,`transitions.${e}`],[`datasets.${t}`,""]]))}datasetElementScopeKeys(t,e){return En(`${t}-${e}`,(()=>[[`datasets.${t}.elements.${e}`,`datasets.${t}`,`elements.${e}`,""]]))}pluginScopeKeys(t){const e=t.id;return En(`${this.type}-plugin-${e}`,(()=>[[`plugins.${e}`,...t.additionalOptionScopes||[]]]))}_cachedScopes(t,e){const i=this._scopeCache;let s=i.get(t);return s&&!e||(s=new Map,i.set(t,s)),s}getOptionScopes(t,e,i){const{options:s,type:n}=this,o=this._cachedScopes(t,i),r=o.get(e);if(r)return r;const a=new Set;e.forEach((e=>{t&&(a.add(t),e.forEach((e=>zn(a,t,e)))),e.forEach((t=>zn(a,s,t))),e.forEach((t=>zn(a,ze[n]||{},t))),e.forEach((t=>zn(a,We,t))),e.forEach((t=>zn(a,Re,t)))}));const h=Array.from(a);return 0===h.length&&h.push(Object.create(null)),Ln.has(e)&&o.set(e,h),h}chartOptionScopes(){const{options:t,type:e}=this;return[t,ze[e]||{},We.datasets[e]||{},{type:e},We,Re]}resolveNamedOptions(t,e,i,s=[""]){const n={$shared:!0},{resolver:o,subPrefixes:r}=Fn(this._resolverCache,t,s);let a=o;if(function(t,e){const{isScriptable:i,isIndexable:s}=fi(t);for(const n of e){const e=i(n),o=s(n),r=(o||e)&&t[n];if(e&&($t(r)||Bn(r))||o&&Ot(r))return!0}return!1}(o,e)){n.$shared=!1;a=ui(o,i=$t(i)?i():i,this.createResolver(t,i,r))}for(const h of e)n[h]=a[h];return n}createResolver(t,e,i=[""],s){const{resolver:n}=Fn(this._resolverCache,t,i);return Dt(e)?ui(n,e,void 0,s):n}}function Fn(t,e,i){let s=t.get(e);s||(s=new Map,t.set(e,s));const n=i.join();let o=s.get(n);if(!o){o={resolver:di(e,i),subPrefixes:i.filter((t=>!t.toLowerCase().includes("hover")))},s.set(n,o)}return o}const Bn=t=>Dt(t)&&Object.getOwnPropertyNames(t).some((e=>$t(t[e])));const Hn=["top","bottom","left","right","chartArea"];function Wn(t,e){return"top"===t||"bottom"===t||-1===Hn.indexOf(t)&&"x"===e}function jn(t,e){return function(i,s){return i[t]===s[t]?i[e]-s[e]:i[t]-s[t]}}function Vn(t){const e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),At(i&&i.onComplete,[t],e)}function Nn(t){const e=t.chart,i=e.options.animation;At(i&&i.onProgress,[t],e)}function $n(t){return Ai()&&"string"==typeof t?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const Yn={},Un=t=>{const e=$n(t);return Object.values(Yn).filter((t=>t.canvas===e)).pop()};function Xn(t,e,i){const s=Object.keys(t);for(const n of s){const s=+n;if(s>=e){const o=t[n];delete t[n],(i>0||s>e)&&(t[s+i]=o)}}}function qn(t,e,i){return t.options.clip?t[i]:e[i]}let Kn=(i(t=class{static register(...t){vn.add(...t),Gn()}static unregister(...t){vn.remove(...t),Gn()}constructor(t,e){const i=this.config=new Rn(e),s=$n(t),n=Un(s);if(n)throw new Error("Canvas is already in use. Chart with ID '"+n.id+"' must be destroyed before the canvas with ID '"+n.canvas.id+"' can be reused.");const o=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||function(t){return!Ai()||"undefined"!=typeof OffscreenCanvas&&t instanceof OffscreenCanvas?Ys:an}(s)),this.platform.updateConfig(i);const r=this.platform.acquireContext(s,o.aspectRatio),a=r&&r.canvas,h=a&&a.height,l=a&&a.width;this.id=Mt(),this.ctx=r,this.canvas=a,this.width=l,this.height=h,this._options=o,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new wn,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(t,e){let i;return function(...s){return e?(clearTimeout(i),i=setTimeout(t,e,s)):t.apply(this,s),e}}((t=>this.update(t)),o.resizeDelay||0),this._dataChanges=[],Yn[this.id]=this,r&&a&&(is.listen(this,"complete",Vn),is.listen(this,"progress",Nn),this._initialize(),this.attached&&this.update())}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:e},width:i,height:s,_aspectRatio:n}=this;return St(t)?e&&n?n:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return vn}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Wi(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Ne(this.canvas,this.ctx),this}stop(){return is.stop(this),this}resize(t,e){is.running(this)?this._resizeBeforeDraw={width:t,height:e}:this._resize(t,e)}_resize(t,e){const i=this.options,s=this.canvas,n=i.maintainAspectRatio&&this.aspectRatio,o=this.platform.getMaximumSize(s,t,e,n),r=i.devicePixelRatio||this.platform.getDevicePixelRatio(),a=this.width?"resize":"attach";this.width=o.width,this.height=o.height,this._aspectRatio=this.aspectRatio,Wi(this,r,!0)&&(this.notifyPlugins("resize",{size:o}),At(i.onResize,[this,o],this),this.attached&&this._doResize(a)&&this.render())}ensureScalesHaveIDs(){It(this.options.scales||{},((t,e)=>{t.id=e}))}buildOrUpdateScales(){const t=this.options,e=t.scales,i=this.scales,s=Object.keys(i).reduce(((t,e)=>(t[e]=!1,t)),{});let n=[];e&&(n=n.concat(Object.keys(e).map((t=>{const i=e[t],s=Dn(t,i),n="r"===s,o="x"===s;return{options:i,dposition:n?"chartArea":o?"bottom":"left",dtype:n?"radialLinear":o?"category":"linear"}})))),It(n,(e=>{const n=e.options,o=n.id,r=Dn(o,n),a=Ct(n.type,e.dtype);void 0!==n.position&&Wn(n.position,r)===Wn(e.dposition)||(n.position=e.dposition),s[o]=!0;let h=null;if(o in i&&i[o].type===a)h=i[o];else{h=new(vn.getScale(a))({id:o,type:a,ctx:this.ctx,chart:this}),i[h.id]=h}h.init(n,t)})),It(s,((t,e)=>{t||delete i[e]})),It(i,(t=>{Ns.configure(this,t,t.options),Ns.addBox(this,t)}))}_updateMetasets(){const t=this._metasets,e=this.data.datasets.length,i=t.length;if(t.sort(((t,e)=>t.index-e.index)),i>e){for(let t=e;t<i;++t)this._destroyDatasetMeta(t);t.splice(e,i-e)}this._sortedMetasets=t.slice(0).sort(jn("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:e}}=this;t.length>e.length&&delete this._stacks,t.forEach(((t,i)=>{0===e.filter((e=>e===t._dataset)).length&&this._destroyDatasetMeta(i)}))}buildOrUpdateControllers(){const t=[],e=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=e.length;i<s;i++){const s=e[i];let n=this.getDatasetMeta(i);const o=s.type||this.config.type;if(n.type&&n.type!==o&&(this._destroyDatasetMeta(i),n=this.getDatasetMeta(i)),n.type=o,n.indexAxis=s.indexAxis||Sn(o,this.options),n.order=s.order||0,n.index=i,n.label=""+s.label,n.visible=this.isDatasetVisible(i),n.controller)n.controller.updateIndex(i),n.controller.linkScales();else{const e=vn.getController(o),{datasetElementType:s,dataElementType:r}=We.datasets[o];Object.assign(e,{dataElementType:vn.getElement(r),datasetElementType:s&&vn.getElement(s)}),n.controller=new e(this,i),t.push(n.controller)}}return this._updateMetasets(),t}_resetElements(){It(this.data.datasets,((t,e)=>{this.getDatasetMeta(e).controller.reset()}),this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const e=this.config;e.update();const i=this._options=e.createResolver(e.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0}))return;const n=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let o=0;for(let h=0,l=this.data.datasets.length;h<l;h++){const{controller:t}=this.getDatasetMeta(h),e=!s&&-1===n.indexOf(t);t.buildOrUpdateElements(e),o=Math.max(+t.getMaxOverflow(),o)}o=this._minPadding=i.layout.autoPadding?o:0,this._updateLayout(o),s||It(n,(t=>{t.reset()})),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(jn("z","_idx"));const{_active:r,_lastEvent:a}=this;a?this._eventHandler(a,!0):r.length&&this._updateHoverStyles(r,r,!0),this.render()}_updateScales(){It(this.scales,(t=>{Ns.removeBox(this,t)})),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,e=new Set(Object.keys(this._listeners)),i=new Set(t.events);Yt(e,i)&&!!this._responsiveListeners===t.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,e=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:n}of e){Xn(t,s,"_removeElements"===i?-n:n)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const e=this.data.datasets.length,i=e=>new Set(t.filter((t=>t[0]===e)).map(((t,e)=>e+","+t.splice(1).join(",")))),s=i(0);for(let n=1;n<e;n++)if(!Yt(s,i(n)))return;return Array.from(s).map((t=>t.split(","))).map((t=>({method:t[1],start:+t[2],count:+t[3]})))}_updateLayout(t){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;Ns.update(this,this.width,this.height,t);const e=this.chartArea,i=e.width<=0||e.height<=0;this._layers=[],It(this.boxes,(t=>{i&&"chartArea"===t.position||(t.configure&&t.configure(),this._layers.push(...t._layers()))}),this),this._layers.forEach(((t,e)=>{t._idx=e})),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})){for(let t=0,e=this.data.datasets.length;t<e;++t)this.getDatasetMeta(t).controller.configure();for(let e=0,i=this.data.datasets.length;e<i;++e)this._updateDataset(e,$t(t)?t({datasetIndex:e}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,e){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:e,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",s)&&(i.controller._update(e),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(is.has(this)?this.attached&&!is.running(this)&&is.start(this):(this.draw(),Vn({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:t,height:e}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(t,e)}if(this.clear(),this.width<=0||this.height<=0)return;if(!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const e=this._layers;for(t=0;t<e.length&&e[t].z<=0;++t)e[t].draw(this.chartArea);for(this._drawDatasets();t<e.length;++t)e[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const e=this._sortedMetasets,i=[];let s,n;for(s=0,n=e.length;s<n;++s){const n=e[s];t&&!n.visible||i.push(n)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const t=this.getSortedVisibleDatasetMetas();for(let e=t.length-1;e>=0;--e)this._drawDataset(t[e]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const e=this.ctx,i=t._clip,s=!i.disabled,n=function(t,e){const{xScale:i,yScale:s}=t;return i&&s?{left:qn(i,e,"left"),right:qn(i,e,"right"),top:qn(s,e,"top"),bottom:qn(s,e,"bottom")}:e}(t,this.chartArea),o={meta:t,index:t.index,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetDraw",o)&&(s&&Xe(e,{left:!1===i.left?0:n.left-i.left,right:!1===i.right?this.width:n.right+i.right,top:!1===i.top?0:n.top-i.top,bottom:!1===i.bottom?this.height:n.bottom+i.bottom}),t.controller.draw(),s&&qe(e),o.cancelable=!1,this.notifyPlugins("afterDatasetDraw",o))}isPointInArea(t){return Ue(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,e,i,s){const n=Cs.modes[e];return"function"==typeof n?n(this,t,i,s):[]}getDatasetMeta(t){const e=this.data.datasets[t],i=this._metasets;let s=i.filter((t=>t&&t._dataset===e)).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:e&&e.order||0,index:t,_dataset:e,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=ci(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const e=this.data.datasets[t];if(!e)return!1;const i=this.getDatasetMeta(t);return"boolean"==typeof i.hidden?!i.hidden:!e.hidden}setDatasetVisibility(t,e){this.getDatasetMeta(t).hidden=!e}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,e,i){const s=i?"show":"hide",n=this.getDatasetMeta(t),o=n.controller._resolveAnimations(void 0,s);Nt(e)?(n.data[e].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),o.update(n,{visible:i}),this.update((e=>e.datasetIndex===t?s:void 0)))}hide(t,e){this._updateVisibility(t,e,!1)}show(t,e){this._updateVisibility(t,e,!0)}_destroyDatasetMeta(t){const e=this._metasets[t];e&&e.controller&&e.controller._destroy(),delete this._metasets[t]}_stop(){let t,e;for(this.stop(),is.remove(this),t=0,e=this.data.datasets.length;t<e;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:e}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Ne(t,e),this.platform.releaseContext(e),this.canvas=null,this.ctx=null),delete Yn[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(t,e,i)=>{t.offsetX=e,t.offsetY=i,this._eventHandler(t)};It(this.options.events,(t=>i(t,s)))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,e=this.platform,i=(i,s)=>{e.addEventListener(this,i,s),t[i]=s},s=(i,s)=>{t[i]&&(e.removeEventListener(this,i,s),delete t[i])},n=(t,e)=>{this.canvas&&this.resize(t,e)};let o;const r=()=>{s("attach",r),this.attached=!0,this.resize(),i("resize",n),i("detach",o)};o=()=>{this.attached=!1,s("resize",n),this._stop(),this._resize(0,0),i("attach",r)},e.isAttached(this.canvas)?r():o()}unbindEvents(){It(this._listeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._listeners={},It(this._responsiveListeners,((t,e)=>{this.platform.removeEventListener(this,e,t)})),this._responsiveListeners=void 0}updateHoverStyle(t,e,i){const s=i?"set":"remove";let n,o,r,a;for("dataset"===e&&(n=this.getDatasetMeta(t[0].datasetIndex),n.controller["_"+s+"DatasetHoverStyle"]()),r=0,a=t.length;r<a;++r){o=t[r];const e=o&&this.getDatasetMeta(o.datasetIndex).controller;e&&e[s+"HoverStyle"](o.element,o.datasetIndex,o.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const e=this._active||[],i=t.map((({datasetIndex:t,index:e})=>{const i=this.getDatasetMeta(t);if(!i)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[e],index:e}}));!Lt(i,e)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,e))}notifyPlugins(t,e,i){return this._plugins.notify(this,t,e,i)}isPluginEnabled(t){return 1===this._plugins._cache.filter((e=>e.plugin.id===t)).length}_updateHoverStyles(t,e,i){const s=this.options.hover,n=(t,e)=>t.filter((t=>!e.some((e=>t.datasetIndex===e.datasetIndex&&t.index===e.index)))),o=n(e,t),r=i?t:n(t,e);o.length&&this.updateHoverStyle(o,s.mode,!1),r.length&&s.mode&&this.updateHoverStyle(r,s.mode,!0)}_eventHandler(t,e){const i={event:t,replay:e,cancelable:!0,inChartArea:this.isPointInArea(t)},s=e=>(e.options.events||this.options.events).includes(t.native.type);if(!1===this.notifyPlugins("beforeEvent",i,s))return;const n=this._handleEvent(t,e,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(n||i.changed)&&this.render(),this}_handleEvent(t,e,i){const{_active:s=[],options:n}=this,o=e,r=this._getActiveElements(t,s,i,o),a=function(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}(t),h=function(t,e,i,s){return i&&"mouseout"!==t.type?s?e:t:null}(t,this._lastEvent,i,a);i&&(this._lastEvent=null,At(n.onHover,[t,r,this],this),a&&At(n.onClick,[t,r,this],this));const l=!Lt(r,s);return(l||e)&&(this._active=r,this._updateHoverStyles(r,s,e)),this._lastEvent=h,l}_getActiveElements(t,e,i,s){if("mouseout"===t.type)return[];if(!i)return e;const n=this.options.hover;return this.getElementsAtEventForMode(t,n.mode,n,s)}},"defaults",We),i(t,"instances",Yn),i(t,"overrides",ze),i(t,"registry",vn),i(t,"version","4.4.6"),i(t,"getChart",Un),t);function Gn(){return It(Kn.instances,(t=>t._plugins.invalidate()))}function Zn(t,e,i=e){t.lineCap=Ct(i.borderCapStyle,e.borderCapStyle),t.setLineDash(Ct(i.borderDash,e.borderDash)),t.lineDashOffset=Ct(i.borderDashOffset,e.borderDashOffset),t.lineJoin=Ct(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=Ct(i.borderWidth,e.borderWidth),t.strokeStyle=Ct(i.borderColor,e.borderColor)}function Qn(t,e,i){t.lineTo(i.x,i.y)}function Jn(t,e,i={}){const s=t.length,{start:n=0,end:o=s-1}=i,{start:r,end:a}=e,h=Math.max(n,r),l=Math.min(o,a),c=n<r&&o<r||n>a&&o>a;return{count:s,start:h,loop:e.loop,ilen:l<h&&!c?s+l-h:l-h}}function to(t,e,i,s){const{points:n,options:o}=e,{count:r,start:a,loop:h,ilen:l}=Jn(n,i,s),c=function(t){return t.stepped?Ke:t.tension||"monotone"===t.cubicInterpolationMode?Ge:Qn}(o);let d,u,f,{move:g=!0,reverse:p}=s||{};for(d=0;d<=l;++d)u=n[(a+(p?l-d:d))%r],u.skip||(g?(t.moveTo(u.x,u.y),g=!1):c(t,f,u,p,o.stepped),f=u);return h&&(u=n[(a+(p?l:0))%r],c(t,f,u,p,o.stepped)),!!h}function eo(t,e,i,s){const n=e.points,{count:o,start:r,ilen:a}=Jn(n,i,s),{move:h=!0,reverse:l}=s||{};let c,d,u,f,g,p,m=0,x=0;const b=t=>(r+(l?a-t:t))%o,_=()=>{f!==g&&(t.lineTo(m,g),t.lineTo(m,f),t.lineTo(m,p))};for(h&&(d=n[b(0)],t.moveTo(d.x,d.y)),c=0;c<=a;++c){if(d=n[b(c)],d.skip)continue;const e=d.x,i=d.y,s=0|e;s===u?(i<f?f=i:i>g&&(g=i),m=(x*m+e)/++x):(_(),t.lineTo(e,i),u=s,x=0,f=g=i),p=i}_()}function io(t){const e=t.options,i=e.borderDash&&e.borderDash.length;return!(t._decimated||t._loop||e.tension||"monotone"===e.cubicInterpolationMode||e.stepped||i)?eo:to}const so="function"==typeof Path2D;function no(t,e,i,s){so&&!e.options.segment?function(t,e,i,s){let n=e._path;n||(n=e._path=new Path2D,e.path(n,i,s)&&n.closePath()),Zn(t,e.options),t.stroke(n)}(t,e,i,s):function(t,e,i,s){const{segments:n,options:o}=e,r=io(e);for(const a of n)Zn(t,o,a.style),t.beginPath(),r(t,e,a,{start:i,end:i+s-1})&&t.closePath(),t.stroke()}(t,e,i,s)}class oo extends hn{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,e){const i=this.options;if((i.tension||"monotone"===i.cubicInterpolationMode)&&!i.stepped&&!this._pointsUpdated){const s=i.spanGaps?this._loop:this._fullLoop;Ci(this._points,i,t,s,e),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(t,e){const i=t.points,s=t.options.spanGaps,n=i.length;if(!n)return[];const o=!!t._loop,{start:r,end:a}=function(t,e,i,s){let n=0,o=e-1;if(i&&!s)for(;n<e&&!t[n].skip;)n++;for(;n<e&&t[n].skip;)n++;for(n%=e,i&&(o+=n);o>n&&t[o%e].skip;)o--;return o%=e,{start:n,end:o}}(i,n,o,s);return Qi(t,!0===s?[{start:r,end:a,loop:o}]:function(t,e,i,s){const n=t.length,o=[];let r,a=e,h=t[e];for(r=e+1;r<=i;++r){const i=t[r%n];i.skip||i.stop?h.skip||(s=!1,o.push({start:e%n,end:(r-1)%n,loop:s}),e=a=i.stop?r:null):(a=r,h.skip&&(e=r)),h=i}return null!==a&&o.push({start:e%n,end:a%n,loop:s}),o}(i,r,a<r?a+n:a,!!t._fullLoop&&0===r&&a===n-1),i,e)}(this,this.options.segment))}first(){const t=this.segments,e=this.points;return t.length&&e[t[0].start]}last(){const t=this.segments,e=this.points,i=t.length;return i&&e[t[i-1].end]}interpolate(t,e){const i=this.options,s=t[e],n=this.points,o=function(t,e){const i=[],s=t.segments;for(let n=0;n<s.length;n++){const o=Zi(s[n],t.points,e);o.length&&i.push(...o)}return i}(this,{property:e,start:s,end:s});if(!o.length)return;const r=[],a=function(t){return t.stepped?$i:t.tension||"monotone"===t.cubicInterpolationMode?Yi:Ni}(i);let h,l;for(h=0,l=o.length;h<l;++h){const{start:l,end:c}=o[h],d=n[l],u=n[c];if(d===u){r.push(d);continue}const f=a(d,u,Math.abs((s-d[e])/(u[e]-d[e])),i.stepped);f[e]=t[e],r.push(f)}return 1===r.length?r[0]:r}pathSegment(t,e,i){return io(this)(t,this,e,i)}path(t,e,i){const s=this.segments,n=io(this);let o=this._loop;e=e||0,i=i||this.points.length-e;for(const r of s)o&=n(t,this,r,{start:e,end:e+i-1});return!!o}draw(t,e,i,s){const n=this.options||{};(this.points||[]).length&&n.borderWidth&&(t.save(),no(t,this,i,s),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function ro(t,e,i,s){const n=t.options,{[i]:o}=t.getProps([i],s);return Math.abs(e-o)<n.radius+n.hitRadius}i(oo,"id","line"),i(oo,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),i(oo,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),i(oo,"descriptors",{_scriptable:!0,_indexable:t=>"borderDash"!==t&&"fill"!==t});class ao extends hn{constructor(t){super(),i(this,"parsed"),i(this,"skip"),i(this,"stop"),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,t&&Object.assign(this,t)}inRange(t,e,i){const s=this.options,{x:n,y:o}=this.getProps(["x","y"],i);return Math.pow(t-n,2)+Math.pow(e-o,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(t,e){return ro(this,t,"x",e)}inYRange(t,e){return ro(this,t,"y",e)}getCenterPoint(t){const{x:e,y:i}=this.getProps(["x","y"],t);return{x:e,y:i}}size(t){let e=(t=t||this.options||{}).radius||0;e=Math.max(e,e&&t.hoverRadius||0);return 2*(e+(e&&t.borderWidth||0))}draw(t,e){const i=this.options;this.skip||i.radius<.1||!Ue(this,e,this.size(i)/2)||(t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.fillStyle=i.backgroundColor,$e(t,i,this.x,this.y))}getRange(){const t=this.options||{};return t.radius+t.hitRadius}}i(ao,"id","point"),i(ao,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),i(ao,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});const ho=(t,e)=>{let{boxHeight:i=e,boxWidth:s=e}=t;return t.usePointStyle&&(i=Math.min(i,e),s=t.pointStyleWidth||Math.min(s,e)),{boxWidth:s,boxHeight:i,itemHeight:Math.max(e,i)}};class lo extends hn{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e,i){this.maxWidth=t,this.maxHeight=e,this._margins=i,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let e=At(t.generateLabels,[this.chart],this)||[];t.filter&&(e=e.filter((e=>t.filter(e,this.chart.data)))),t.sort&&(e=e.sort(((e,i)=>t.sort(e,i,this.chart.data)))),this.options.reverse&&e.reverse(),this.legendItems=e}fit(){const{options:t,ctx:e}=this;if(!t.display)return void(this.width=this.height=0);const i=t.labels,s=ai(i.font),n=s.size,o=this._computeTitleHeight(),{boxWidth:r,itemHeight:a}=ho(i,n);let h,l;e.font=s.string,this.isHorizontal()?(h=this.maxWidth,l=this._fitRows(o,n,r,a)+10):(l=this.maxHeight,h=this._fitCols(o,s,r,a)+10),this.width=Math.min(h,t.maxWidth||this.maxWidth),this.height=Math.min(l,t.maxHeight||this.maxHeight)}_fitRows(t,e,i,s){const{ctx:n,maxWidth:o,options:{labels:{padding:r}}}=this,a=this.legendHitBoxes=[],h=this.lineWidths=[0],l=s+r;let c=t;n.textAlign="left",n.textBaseline="middle";let d=-1,u=-l;return this.legendItems.forEach(((t,f)=>{const g=i+e/2+n.measureText(t.text).width;(0===f||h[h.length-1]+g+2*r>o)&&(c+=l,h[h.length-(f>0?0:1)]=0,u+=l,d++),a[f]={left:0,top:u,row:d,width:g,height:s},h[h.length-1]+=g+r})),c}_fitCols(t,e,i,s){const{ctx:n,maxHeight:o,options:{labels:{padding:r}}}=this,a=this.legendHitBoxes=[],h=this.columnSizes=[],l=o-t;let c=r,d=0,u=0,f=0,g=0;return this.legendItems.forEach(((t,o)=>{const{itemWidth:p,itemHeight:m}=function(t,e,i,s,n){const o=function(t,e,i,s){let n=t.text;n&&"string"!=typeof n&&(n=n.reduce(((t,e)=>t.length>e.length?t:e)));return e+i.size/2+s.measureText(n).width}(s,t,e,i),r=function(t,e,i){let s=t;"string"!=typeof e.text&&(s=co(e,i));return s}(n,s,e.lineHeight);return{itemWidth:o,itemHeight:r}}(i,e,n,t,s);o>0&&u+m+2*r>l&&(c+=d+r,h.push({width:d,height:u}),f+=d+r,g++,d=u=0),a[o]={left:f,top:u,col:g,width:p,height:m},d=Math.max(d,p),u+=m+r})),c+=d,h.push({width:d,height:u}),c}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:e,options:{align:i,labels:{padding:s},rtl:n}}=this,o=Ui(n,this.left,this.width);if(this.isHorizontal()){let n=0,r=ve(i,this.left+s,this.right-this.lineWidths[n]);for(const a of e)n!==a.row&&(n=a.row,r=ve(i,this.left+s,this.right-this.lineWidths[n])),a.top+=this.top+t+s,a.left=o.leftForLtr(o.x(r),a.width),r+=a.width+s}else{let n=0,r=ve(i,this.top+t+s,this.bottom-this.columnSizes[n].height);for(const a of e)a.col!==n&&(n=a.col,r=ve(i,this.top+t+s,this.bottom-this.columnSizes[n].height)),a.top=r,a.left+=this.left+s,a.left=o.leftForLtr(o.x(a.left),a.width),r+=a.height+s}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const t=this.ctx;Xe(t,this),this._draw(),qe(t)}}_draw(){const{options:t,columnSizes:e,lineWidths:i,ctx:s}=this,{align:n,labels:o}=t,r=We.color,a=Ui(t.rtl,this.left,this.width),h=ai(o.font),{padding:l}=o,c=h.size,d=c/2;let u;this.drawTitle(),s.textAlign=a.textAlign("left"),s.textBaseline="middle",s.lineWidth=.5,s.font=h.string;const{boxWidth:f,boxHeight:g,itemHeight:p}=ho(o,c),m=this.isHorizontal(),x=this._computeTitleHeight();u=m?{x:ve(n,this.left+l,this.right-i[0]),y:this.top+l+x,line:0}:{x:this.left+l,y:ve(n,this.top+x+l,this.bottom-e[0].height),line:0},Xi(this.ctx,t.textDirection);const b=p+l;this.legendItems.forEach(((_,y)=>{s.strokeStyle=_.fontColor,s.fillStyle=_.fontColor;const v=s.measureText(_.text).width,w=a.textAlign(_.textAlign||(_.textAlign=o.textAlign)),k=f+d+v;let M=u.x,S=u.y;a.setWidth(this.width),m?y>0&&M+k+l>this.right&&(S=u.y+=b,u.line++,M=u.x=ve(n,this.left+l,this.right-i[u.line])):y>0&&S+b>this.bottom&&(M=u.x=M+e[u.line].width+l,u.line++,S=u.y=ve(n,this.top+x+l,this.bottom-e[u.line].height));if(function(t,e,i){if(isNaN(f)||f<=0||isNaN(g)||g<0)return;s.save();const n=Ct(i.lineWidth,1);if(s.fillStyle=Ct(i.fillStyle,r),s.lineCap=Ct(i.lineCap,"butt"),s.lineDashOffset=Ct(i.lineDashOffset,0),s.lineJoin=Ct(i.lineJoin,"miter"),s.lineWidth=n,s.strokeStyle=Ct(i.strokeStyle,r),s.setLineDash(Ct(i.lineDash,[])),o.usePointStyle){const r={radius:g*Math.SQRT2/2,pointStyle:i.pointStyle,rotation:i.rotation,borderWidth:n},h=a.xPlus(t,f/2);Ye(s,r,h,e+d,o.pointStyleWidth&&f)}else{const o=e+Math.max((c-g)/2,0),r=a.leftForLtr(t,f),h=oi(i.borderRadius);s.beginPath(),Object.values(h).some((t=>0!==t))?ti(s,{x:r,y:o,w:f,h:g,radius:h}):s.rect(r,o,f,g),s.fill(),0!==n&&s.stroke()}s.restore()}(a.x(M),S,_),M=((t,e,i,s)=>t===(s?"left":"right")?i:"center"===t?(e+i)/2:e)(w,M+f+d,m?M+k:this.right,t.rtl),function(t,e,i){Je(s,i.text,t,e+p/2,h,{strikethrough:i.hidden,textAlign:a.textAlign(i.textAlign)})}(a.x(M),S,_),m)u.x+=k+l;else if("string"!=typeof _.text){const t=h.lineHeight;u.y+=co(_,t)+l}else u.y+=b})),qi(this.ctx,t.textDirection)}drawTitle(){const t=this.options,e=t.title,i=ai(e.font),s=ri(e.padding);if(!e.display)return;const n=Ui(t.rtl,this.left,this.width),o=this.ctx,r=e.position,a=i.size/2,h=s.top+a;let l,c=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),l=this.top+h,c=ve(t.align,c,this.right-d);else{const e=this.columnSizes.reduce(((t,e)=>Math.max(t,e.height)),0);l=h+ve(t.align,this.top,this.bottom-e-t.labels.padding-this._computeTitleHeight())}const u=ve(r,c,c+d);o.textAlign=n.textAlign(ye(r)),o.textBaseline="middle",o.strokeStyle=e.color,o.fillStyle=e.color,o.font=i.string,Je(o,e.text,u,l,i)}_computeTitleHeight(){const t=this.options.title,e=ai(t.font),i=ri(t.padding);return t.display?e.lineHeight+i.height:0}_getLegendItemAt(t,e){let i,s,n;if(ue(t,this.left,this.right)&&ue(e,this.top,this.bottom))for(n=this.legendHitBoxes,i=0;i<n.length;++i)if(s=n[i],ue(t,s.left,s.left+s.width)&&ue(e,s.top,s.top+s.height))return this.legendItems[i];return null}handleEvent(t){const e=this.options;if(!function(t,e){if(("mousemove"===t||"mouseout"===t)&&(e.onHover||e.onLeave))return!0;if(e.onClick&&("click"===t||"mouseup"===t))return!0;return!1}(t.type,e))return;const i=this._getLegendItemAt(t.x,t.y);if("mousemove"===t.type||"mouseout"===t.type){const o=this._hoveredItem,r=(n=i,null!==(s=o)&&null!==n&&s.datasetIndex===n.datasetIndex&&s.index===n.index);o&&!r&&At(e.onLeave,[t,o,this],this),this._hoveredItem=i,i&&!r&&At(e.onHover,[t,i,this],this)}else i&&At(e.onClick,[t,i,this],this);var s,n}}function co(t,e){return e*(t.text?t.text.length:0)}var uo={id:"legend",_element:lo,start(t,e,i){const s=t.legend=new lo({ctx:t.ctx,options:i,chart:t});Ns.configure(t,s,i),Ns.addBox(t,s)},stop(t){Ns.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){const s=t.legend;Ns.configure(t,s,i),s.options=i},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){const s=e.datasetIndex,n=i.chart;n.isDatasetVisible(s)?(n.hide(s),e.hidden=!0):(n.show(s),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:s,textAlign:n,color:o,useBorderRadius:r,borderRadius:a}}=t.legend.options;return t._getSortedDatasetMetas().map((t=>{const h=t.controller.getStyle(i?0:void 0),l=ri(h.borderWidth);return{text:e[t.index].label,fillStyle:h.backgroundColor,fontColor:o,hidden:!t.visible,lineCap:h.borderCapStyle,lineDash:h.borderDash,lineDashOffset:h.borderDashOffset,lineJoin:h.borderJoinStyle,lineWidth:(l.width+l.height)/4,strokeStyle:h.borderColor,pointStyle:s||h.pointStyle,rotation:h.rotation,textAlign:n||h.textAlign,borderRadius:r&&(a||h.borderRadius),datasetIndex:t.index}}),this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class fo extends hn{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,e){const i=this.options;if(this.left=0,this.top=0,!i.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=t,this.height=this.bottom=e;const s=Ot(i.text)?i.text.length:1;this._padding=ri(i.padding);const n=s*ai(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=n:this.width=n}isHorizontal(){const t=this.options.position;return"top"===t||"bottom"===t}_drawArgs(t){const{top:e,left:i,bottom:s,right:n,options:o}=this,r=o.align;let a,h,l,c=0;return this.isHorizontal()?(h=ve(r,i,n),l=e+t,a=n-i):("left"===o.position?(h=i+t,l=ve(r,s,e),c=-.5*Ut):(h=n-t,l=ve(r,e,s),c=.5*Ut),a=s-e),{titleX:h,titleY:l,maxWidth:a,rotation:c}}draw(){const t=this.ctx,e=this.options;if(!e.display)return;const i=ai(e.font),s=i.lineHeight/2+this._padding.top,{titleX:n,titleY:o,maxWidth:r,rotation:a}=this._drawArgs(s);Je(t,e.text,0,0,i,{color:e.color,maxWidth:r,rotation:a,textAlign:ye(e.align),textBaseline:"middle",translation:[n,o]})}}var go={id:"title",_element:fo,start(t,e,i){!function(t,e){const i=new fo({ctx:t.ctx,options:e,chart:t});Ns.configure(t,i,e),Ns.addBox(t,i),t.titleBlock=i}(t,i)},stop(t){const e=t.titleBlock;Ns.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){const s=t.titleBlock;Ns.configure(t,s,i),s.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const po={average(t){if(!t.length)return!1;let e,i,s=new Set,n=0,o=0;for(e=0,i=t.length;e<i;++e){const i=t[e].element;if(i&&i.hasValue()){const t=i.tooltipPosition();s.add(t.x),n+=t.y,++o}}if(0===o||0===s.size)return!1;return{x:[...s].reduce(((t,e)=>t+e))/s.size,y:n/o}},nearest(t,e){if(!t.length)return!1;let i,s,n,o=e.x,r=e.y,a=Number.POSITIVE_INFINITY;for(i=0,s=t.length;i<s;++i){const s=t[i].element;if(s&&s.hasValue()){const t=ae(e,s.getCenterPoint());t<a&&(a=t,n=s)}}if(n){const t=n.tooltipPosition();o=t.x,r=t.y}return{x:o,y:r}}};function mo(t,e){return e&&(Ot(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function xo(t){return("string"==typeof t||t instanceof String)&&t.indexOf("\n")>-1?t.split("\n"):t}function bo(t,e){const{element:i,datasetIndex:s,index:n}=e,o=t.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(n);return{chart:t,label:r,parsed:o.getParsed(n),raw:t.data.datasets[s].data[n],formattedValue:a,dataset:o.getDataset(),dataIndex:n,datasetIndex:s,element:i}}function _o(t,e){const i=t.chart.ctx,{body:s,footer:n,title:o}=t,{boxWidth:r,boxHeight:a}=e,h=ai(e.bodyFont),l=ai(e.titleFont),c=ai(e.footerFont),d=o.length,u=n.length,f=s.length,g=ri(e.padding);let p=g.height,m=0,x=s.reduce(((t,e)=>t+e.before.length+e.lines.length+e.after.length),0);if(x+=t.beforeBody.length+t.afterBody.length,d&&(p+=d*l.lineHeight+(d-1)*e.titleSpacing+e.titleMarginBottom),x){p+=f*(e.displayColors?Math.max(a,h.lineHeight):h.lineHeight)+(x-f)*h.lineHeight+(x-1)*e.bodySpacing}u&&(p+=e.footerMarginTop+u*c.lineHeight+(u-1)*e.footerSpacing);let b=0;const _=function(t){m=Math.max(m,i.measureText(t).width+b)};return i.save(),i.font=l.string,It(t.title,_),i.font=h.string,It(t.beforeBody.concat(t.afterBody),_),b=e.displayColors?r+2+e.boxPadding:0,It(s,(t=>{It(t.before,_),It(t.lines,_),It(t.after,_)})),b=0,i.font=c.string,It(t.footer,_),i.restore(),m+=g.width,{width:m,height:p}}function yo(t,e,i,s){const{x:n,width:o}=i,{width:r,chartArea:{left:a,right:h}}=t;let l="center";return"center"===s?l=n<=(a+h)/2?"left":"right":n<=o/2?l="left":n>=r-o/2&&(l="right"),function(t,e,i,s){const{x:n,width:o}=s,r=i.caretSize+i.caretPadding;return"left"===t&&n+o+r>e.width||"right"===t&&n-o-r<0||void 0}(l,t,e,i)&&(l="center"),l}function vo(t,e,i){const s=i.yAlign||e.yAlign||function(t,e){const{y:i,height:s}=e;return i<s/2?"top":i>t.height-s/2?"bottom":"center"}(t,i);return{xAlign:i.xAlign||e.xAlign||yo(t,e,i,s),yAlign:s}}function wo(t,e,i,s){const{caretSize:n,caretPadding:o,cornerRadius:r}=t,{xAlign:a,yAlign:h}=i,l=n+o,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=oi(r);let g=function(t,e){let{x:i,width:s}=t;return"right"===e?i-=s:"center"===e&&(i-=s/2),i}(e,a);const p=function(t,e,i){let{y:s,height:n}=t;return"top"===e?s+=i:s-="bottom"===e?n+i:n/2,s}(e,h,l);return"center"===h?"left"===a?g+=l:"right"===a&&(g-=l):"left"===a?g-=Math.max(c,u)+n:"right"===a&&(g+=Math.max(d,f)+n),{x:de(g,0,s.width-e.width),y:de(p,0,s.height-e.height)}}function ko(t,e,i){const s=ri(i.padding);return"center"===e?t.x+t.width/2:"right"===e?t.x+t.width-s.right:t.x+s.left}function Mo(t){return mo([],xo(t))}function So(t,e){const i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}const Oo={beforeTitle:kt,title(t){if(t.length>0){const e=t[0],i=e.chart.data.labels,s=i?i.length:0;if(this&&this.options&&"dataset"===this.options.mode)return e.dataset.label||"";if(e.label)return e.label;if(s>0&&e.dataIndex<s)return i[e.dataIndex]}return""},afterTitle:kt,beforeBody:kt,beforeLabel:kt,label(t){if(this&&this.options&&"dataset"===this.options.mode)return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const i=t.formattedValue;return St(i)||(e+=i),e},labelColor(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:e.borderColor,backgroundColor:e.backgroundColor,borderWidth:e.borderWidth,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const e=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:e.pointStyle,rotation:e.rotation}},afterLabel:kt,afterBody:kt,beforeFooter:kt,footer:kt,afterFooter:kt};function Do(t,e,i,s){const n=t[e].call(i,s);return void 0===n?Oo[e].call(i,s):n}class Po extends hn{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const e=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&e.options.animation&&i.animations,n=new rs(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(n)),n}getContext(){return this.$context||(this.$context=(t=this.chart.getContext(),e=this,i=this._tooltipItems,ci(t,{tooltip:e,tooltipItems:i,type:"tooltip"})));var t,e,i}getTitle(t,e){const{callbacks:i}=e,s=Do(i,"beforeTitle",this,t),n=Do(i,"title",this,t),o=Do(i,"afterTitle",this,t);let r=[];return r=mo(r,xo(s)),r=mo(r,xo(n)),r=mo(r,xo(o)),r}getBeforeBody(t,e){return Mo(Do(e.callbacks,"beforeBody",this,t))}getBody(t,e){const{callbacks:i}=e,s=[];return It(t,(t=>{const e={before:[],lines:[],after:[]},n=So(i,t);mo(e.before,xo(Do(n,"beforeLabel",this,t))),mo(e.lines,Do(n,"label",this,t)),mo(e.after,xo(Do(n,"afterLabel",this,t))),s.push(e)})),s}getAfterBody(t,e){return Mo(Do(e.callbacks,"afterBody",this,t))}getFooter(t,e){const{callbacks:i}=e,s=Do(i,"beforeFooter",this,t),n=Do(i,"footer",this,t),o=Do(i,"afterFooter",this,t);let r=[];return r=mo(r,xo(s)),r=mo(r,xo(n)),r=mo(r,xo(o)),r}_createItems(t){const e=this._active,i=this.chart.data,s=[],n=[],o=[];let r,a,h=[];for(r=0,a=e.length;r<a;++r)h.push(bo(this.chart,e[r]));return t.filter&&(h=h.filter(((e,s,n)=>t.filter(e,s,n,i)))),t.itemSort&&(h=h.sort(((e,s)=>t.itemSort(e,s,i)))),It(h,(e=>{const i=So(t.callbacks,e);s.push(Do(i,"labelColor",this,e)),n.push(Do(i,"labelPointStyle",this,e)),o.push(Do(i,"labelTextColor",this,e))})),this.labelColors=s,this.labelPointStyles=n,this.labelTextColors=o,this.dataPoints=h,h}update(t,e){const i=this.options.setContext(this.getContext()),s=this._active;let n,o=[];if(s.length){const t=po[i.position].call(this,s,this._eventPosition);o=this._createItems(i),this.title=this.getTitle(o,i),this.beforeBody=this.getBeforeBody(o,i),this.body=this.getBody(o,i),this.afterBody=this.getAfterBody(o,i),this.footer=this.getFooter(o,i);const e=this._size=_o(this,i),r=Object.assign({},t,e),a=vo(this.chart,i,r),h=wo(i,r,a,this.chart);this.xAlign=a.xAlign,this.yAlign=a.yAlign,n={opacity:1,x:h.x,y:h.y,width:e.width,height:e.height,caretX:t.x,caretY:t.y}}else 0!==this.opacity&&(n={opacity:0});this._tooltipItems=o,this.$context=void 0,n&&this._resolveAnimations().update(this,n),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:e})}drawCaret(t,e,i,s){const n=this.getCaretPosition(t,i,s);e.lineTo(n.x1,n.y1),e.lineTo(n.x2,n.y2),e.lineTo(n.x3,n.y3)}getCaretPosition(t,e,i){const{xAlign:s,yAlign:n}=this,{caretSize:o,cornerRadius:r}=i,{topLeft:a,topRight:h,bottomLeft:l,bottomRight:c}=oi(r),{x:d,y:u}=t,{width:f,height:g}=e;let p,m,x,b,_,y;return"center"===n?(_=u+g/2,"left"===s?(p=d,m=p-o,b=_+o,y=_-o):(p=d+f,m=p+o,b=_-o,y=_+o),x=p):(m="left"===s?d+Math.max(a,l)+o:"right"===s?d+f-Math.max(h,c)-o:this.caretX,"top"===n?(b=u,_=b-o,p=m-o,x=m+o):(b=u+g,_=b+o,p=m+o,x=m-o),y=b),{x1:p,x2:m,x3:x,y1:b,y2:_,y3:y}}drawTitle(t,e,i){const s=this.title,n=s.length;let o,r,a;if(n){const h=Ui(i.rtl,this.x,this.width);for(t.x=ko(this,i.titleAlign,i),e.textAlign=h.textAlign(i.titleAlign),e.textBaseline="middle",o=ai(i.titleFont),r=i.titleSpacing,e.fillStyle=i.titleColor,e.font=o.string,a=0;a<n;++a)e.fillText(s[a],h.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+r,a+1===n&&(t.y+=i.titleMarginBottom-r)}}_drawColorBox(t,e,i,s,n){const o=this.labelColors[i],r=this.labelPointStyles[i],{boxHeight:a,boxWidth:h}=n,l=ai(n.bodyFont),c=ko(this,"left",n),d=s.x(c),u=a<l.lineHeight?(l.lineHeight-a)/2:0,f=e.y+u;if(n.usePointStyle){const e={radius:Math.min(h,a)/2,pointStyle:r.pointStyle,rotation:r.rotation,borderWidth:1},i=s.leftForLtr(d,h)+h/2,l=f+a/2;t.strokeStyle=n.multiKeyBackground,t.fillStyle=n.multiKeyBackground,$e(t,e,i,l),t.strokeStyle=o.borderColor,t.fillStyle=o.backgroundColor,$e(t,e,i,l)}else{t.lineWidth=Dt(o.borderWidth)?Math.max(...Object.values(o.borderWidth)):o.borderWidth||1,t.strokeStyle=o.borderColor,t.setLineDash(o.borderDash||[]),t.lineDashOffset=o.borderDashOffset||0;const e=s.leftForLtr(d,h),i=s.leftForLtr(s.xPlus(d,1),h-2),r=oi(o.borderRadius);Object.values(r).some((t=>0!==t))?(t.beginPath(),t.fillStyle=n.multiKeyBackground,ti(t,{x:e,y:f,w:h,h:a,radius:r}),t.fill(),t.stroke(),t.fillStyle=o.backgroundColor,t.beginPath(),ti(t,{x:i,y:f+1,w:h-2,h:a-2,radius:r}),t.fill()):(t.fillStyle=n.multiKeyBackground,t.fillRect(e,f,h,a),t.strokeRect(e,f,h,a),t.fillStyle=o.backgroundColor,t.fillRect(i,f+1,h-2,a-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,e,i){const{body:s}=this,{bodySpacing:n,bodyAlign:o,displayColors:r,boxHeight:a,boxWidth:h,boxPadding:l}=i,c=ai(i.bodyFont);let d=c.lineHeight,u=0;const f=Ui(i.rtl,this.x,this.width),g=function(i){e.fillText(i,f.x(t.x+u),t.y+d/2),t.y+=d+n},p=f.textAlign(o);let m,x,b,_,y,v,w;for(e.textAlign=o,e.textBaseline="middle",e.font=c.string,t.x=ko(this,p,i),e.fillStyle=i.bodyColor,It(this.beforeBody,g),u=r&&"right"!==p?"center"===o?h/2+l:h+2+l:0,_=0,v=s.length;_<v;++_){for(m=s[_],x=this.labelTextColors[_],e.fillStyle=x,It(m.before,g),b=m.lines,r&&b.length&&(this._drawColorBox(e,t,_,f,i),d=Math.max(c.lineHeight,a)),y=0,w=b.length;y<w;++y)g(b[y]),d=c.lineHeight;It(m.after,g)}u=0,d=c.lineHeight,It(this.afterBody,g),t.y-=n}drawFooter(t,e,i){const s=this.footer,n=s.length;let o,r;if(n){const a=Ui(i.rtl,this.x,this.width);for(t.x=ko(this,i.footerAlign,i),t.y+=i.footerMarginTop,e.textAlign=a.textAlign(i.footerAlign),e.textBaseline="middle",o=ai(i.footerFont),e.fillStyle=i.footerColor,e.font=o.string,r=0;r<n;++r)e.fillText(s[r],a.x(t.x),t.y+o.lineHeight/2),t.y+=o.lineHeight+i.footerSpacing}}drawBackground(t,e,i,s){const{xAlign:n,yAlign:o}=this,{x:r,y:a}=t,{width:h,height:l}=i,{topLeft:c,topRight:d,bottomLeft:u,bottomRight:f}=oi(s.cornerRadius);e.fillStyle=s.backgroundColor,e.strokeStyle=s.borderColor,e.lineWidth=s.borderWidth,e.beginPath(),e.moveTo(r+c,a),"top"===o&&this.drawCaret(t,e,i,s),e.lineTo(r+h-d,a),e.quadraticCurveTo(r+h,a,r+h,a+d),"center"===o&&"right"===n&&this.drawCaret(t,e,i,s),e.lineTo(r+h,a+l-f),e.quadraticCurveTo(r+h,a+l,r+h-f,a+l),"bottom"===o&&this.drawCaret(t,e,i,s),e.lineTo(r+u,a+l),e.quadraticCurveTo(r,a+l,r,a+l-u),"center"===o&&"left"===n&&this.drawCaret(t,e,i,s),e.lineTo(r,a+c),e.quadraticCurveTo(r,a,r+c,a),e.closePath(),e.fill(),s.borderWidth>0&&e.stroke()}_updateAnimationTarget(t){const e=this.chart,i=this.$animations,s=i&&i.x,n=i&&i.y;if(s||n){const i=po[t.position].call(this,this._active,this._eventPosition);if(!i)return;const o=this._size=_o(this,t),r=Object.assign({},i,this._size),a=vo(e,t,r),h=wo(t,r,a,e);s._to===h.x&&n._to===h.y||(this.xAlign=a.xAlign,this.yAlign=a.yAlign,this.width=o.width,this.height=o.height,this.caretX=i.x,this.caretY=i.y,this._resolveAnimations().update(this,h))}}_willRender(){return!!this.opacity}draw(t){const e=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(e);const s={width:this.width,height:this.height},n={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const o=ri(e.padding),r=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;e.enabled&&r&&(t.save(),t.globalAlpha=i,this.drawBackground(n,t,s,e),Xi(t,e.textDirection),n.y+=o.top,this.drawTitle(n,t,e),this.drawBody(n,t,e),this.drawFooter(n,t,e),qi(t,e.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,e){const i=this._active,s=t.map((({datasetIndex:t,index:e})=>{const i=this.chart.getDatasetMeta(t);if(!i)throw new Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[e],index:e}})),n=!Lt(i,s),o=this._positionChanged(s,e);(n||o)&&(this._active=s,this._eventPosition=e,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,e,i=!0){if(e&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,n=this._active||[],o=this._getActiveElements(t,n,e,i),r=this._positionChanged(o,t),a=e||!Lt(o,n)||r;return a&&(this._active=o,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,e))),a}_getActiveElements(t,e,i,s){const n=this.options;if("mouseout"===t.type)return[];if(!s)return e.filter((t=>this.chart.data.datasets[t.datasetIndex]&&void 0!==this.chart.getDatasetMeta(t.datasetIndex).controller.getParsed(t.index)));const o=this.chart.getElementsAtEventForMode(t,n.mode,n,i);return n.reverse&&o.reverse(),o}_positionChanged(t,e){const{caretX:i,caretY:s,options:n}=this,o=po[n.position].call(this,t,e);return!1!==o&&(i!==o.x||s!==o.y)}}i(Po,"positioners",po);var To={id:"tooltip",_element:Po,positioners:po,afterInit(t,e,i){i&&(t.tooltip=new Po({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const i={tooltip:e};if(!1===t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0}))return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){const i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Oo},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>"filter"!==t&&"itemSort"!==t&&"external"!==t,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};function Co(t,e,i,s){const n=t.indexOf(e);if(-1===n)return((t,e,i,s)=>("string"==typeof e?(i=t.push(e)-1,s.unshift({index:i,label:e})):isNaN(e)&&(i=null),i))(t,e,i,s);return n!==t.lastIndexOf(e)?i:n}function Ao(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class Io extends bn{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const e=this._addedLabels;if(e.length){const t=this.getLabels();for(const{index:i,label:s}of e)t[i]===s&&t.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,e){if(St(t))return null;const i=this.getLabels();return((t,e)=>null===t?null:de(Math.round(t),0,e))(e=isFinite(e)&&i[e]===t?e:Co(i,t,Ct(e,t),this._addedLabels),i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:e}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);"ticks"===this.options.bounds&&(t||(i=0),e||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,e=this.max,i=this.options.offset,s=[];let n=this.getLabels();n=0===t&&e===n.length-1?n:n.slice(t,e+1),this._valueRange=Math.max(n.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let o=t;o<=e;o++)s.push({value:o});return s}getLabelForValue(t){return Ao.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return"number"!=typeof t&&(t=this.parse(t)),null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const e=this.ticks;return t<0||t>e.length-1?null:this.getPixelForValue(e[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}function Lo(t,e){const i=[],{bounds:s,step:n,min:o,max:r,precision:a,count:h,maxTicks:l,maxDigits:c,includeBounds:d}=t,u=n||1,f=l-1,{min:g,max:p}=e,m=!St(o),x=!St(r),b=!St(h),_=(p-g)/(c+1);let y,v,w,k,M=se((p-g)/f/u)*u;if(M<1e-14&&!m&&!x)return[{value:g},{value:p}];k=Math.ceil(p/M)-Math.floor(g/M),k>f&&(M=se(k*M/f/u)*u),St(a)||(y=Math.pow(10,a),M=Math.ceil(M*y)/y),"ticks"===s?(v=Math.floor(g/M)*M,w=Math.ceil(p/M)*M):(v=g,w=p),m&&x&&n&&function(t,e){const i=Math.round(t);return i-e<=t&&i+e>=t}((r-o)/n,M/1e3)?(k=Math.round(Math.min((r-o)/M,l)),M=(r-o)/k,v=o,w=r):b?(v=m?o:v,w=x?r:w,k=h-1,M=(w-v)/k):(k=(w-v)/M,k=ie(k,Math.round(k),M/1e3)?Math.round(k):Math.ceil(k));const S=Math.max(re(M),re(v));y=Math.pow(10,St(a)?S:a),v=Math.round(v*y)/y,w=Math.round(w*y)/y;let O=0;for(m&&(d&&v!==o?(i.push({value:o}),v<o&&O++,ie(Math.round((v+O*M)*y)/y,o,Eo(o,_,t))&&O++):v<o&&O++);O<k;++O){const t=Math.round((v+O*M)*y)/y;if(x&&t>r)break;i.push({value:t})}return x&&d&&w!==r?i.length&&ie(i[i.length-1].value,r,Eo(r,_,t))?i[i.length-1].value=r:i.push({value:r}):x&&w!==r||i.push({value:w}),i}function Eo(t,e,{horizontal:i,minRotation:s}){const n=oe(s),o=(i?Math.sin(n):Math.cos(n))||.001,r=.75*e*(""+t).length;return Math.min(e/o,r)}i(Io,"id","category"),i(Io,"defaults",{ticks:{callback:Ao}});class zo extends bn{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,e){return St(t)||("number"==typeof t||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:s,max:n}=this;const o=t=>s=e?s:t,r=t=>n=i?n:t;if(t){const t=ee(s),e=ee(n);t<0&&e<0?r(0):t>0&&e>0&&o(0)}if(s===n){let e=0===n?1:Math.abs(.05*n);r(n+e),t||o(s-e)}this.min=s,this.max=n}getTickLimit(){const t=this.options.ticks;let e,{maxTicksLimit:i,stepSize:s}=t;return s?(e=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,e>1e3&&(e=1e3)):(e=this.computeTickLimit(),i=i||11),i&&(e=Math.min(i,e)),e}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,e=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s=Lo({maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:e.precision,step:e.stepSize,count:e.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:e.minRotation||0,includeBounds:!1!==e.includeBounds},this._range||this);return"ticks"===t.bounds&&function(t,e,i){let s,n,o;for(s=0,n=t.length;s<n;s++)o=t[s][i],isNaN(o)||(e.min=Math.min(e.min,o),e.max=Math.max(e.max,o))}(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}configure(){const t=this.ticks;let e=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-e)/Math.max(t.length-1,1)/2;e-=s,i+=s}this._startValue=e,this._endValue=i,this._valueRange=i-e}getLabelForValue(t){return Ie(t,this.chart.options.locale,this.options.ticks.format)}}class Ro extends zo{determineDataLimits(){const{min:t,max:e}=this.getMinMax(!0);this.min=Pt(t)?t:0,this.max=Pt(e)?e:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),e=t?this.width:this.height,i=oe(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,n=this._resolveTickFontOptions(0);return Math.ceil(e/Math.min(40,n.lineHeight/s))}getPixelForValue(t){return null===t?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}i(Ro,"id","linear"),i(Ro,"defaults",{ticks:{callback:Ee.formatters.numeric}});const Fo={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Bo=Object.keys(Fo);function Ho(t,e){return t-e}function Wo(t,e){if(St(e))return null;const i=t._adapter,{parser:s,round:n,isoWeekday:o}=t._parseOpts;let r=e;return"function"==typeof s&&(r=s(r)),Pt(r)||(r="string"==typeof s?i.parse(r,s):i.parse(r)),null===r?null:(n&&(r="week"!==n||!ne(o)&&!0!==o?i.startOf(r,n):i.startOf(r,"isoWeek",o)),+r)}function jo(t,e,i,s){const n=Bo.length;for(let o=Bo.indexOf(t);o<n-1;++o){const t=Fo[Bo[o]],n=t.steps?t.steps:Number.MAX_SAFE_INTEGER;if(t.common&&Math.ceil((i-e)/(n*t.size))<=s)return Bo[o]}return Bo[n-1]}function Vo(t,e,i){if(i){if(i.length){const{lo:s,hi:n}=fe(i,e);t[i[s]>=e?i[s]:i[n]]=!0}}else t[e]=!0}function No(t,e,i){const s=[],n={},o=e.length;let r,a;for(r=0;r<o;++r)a=e[r],n[a]=r,s.push({value:a,major:!1});return 0!==o&&i?function(t,e,i,s){const n=t._adapter,o=+n.startOf(e[0].value,s),r=e[e.length-1].value;let a,h;for(a=o;a<=r;a=+n.add(a,1,s))h=i[a],h>=0&&(e[h].major=!0);return e}(t,s,n,i):s}class $o extends bn{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,e={}){const i=t.time||(t.time={}),s=this._adapter=new ws._date(t.adapters.date);s.init(e),Bt(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=e.normalized}parse(t,e){return void 0===t?null:Wo(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,e=this._adapter,i=t.time.unit||"day";let{min:s,max:n,minDefined:o,maxDefined:r}=this.getUserBounds();function a(t){o||isNaN(t.min)||(s=Math.min(s,t.min)),r||isNaN(t.max)||(n=Math.max(n,t.max))}o&&r||(a(this._getLabelBounds()),"ticks"===t.bounds&&"labels"===t.ticks.source||a(this.getMinMax(!1))),s=Pt(s)&&!isNaN(s)?s:+e.startOf(Date.now(),i),n=Pt(n)&&!isNaN(n)?n:+e.endOf(Date.now(),i)+1,this.min=Math.min(s,n-1),this.max=Math.max(s+1,n)}_getLabelBounds(){const t=this.getLabelTimestamps();let e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(e=t[0],i=t[t.length-1]),{min:e,max:i}}buildTicks(){const t=this.options,e=t.time,i=t.ticks,s="labels"===i.source?this.getLabelTimestamps():this._generate();"ticks"===t.bounds&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const n=this.min,o=function(t,e,i){let s=0,n=t.length;for(;s<n&&t[s]<e;)s++;for(;n>s&&t[n-1]>i;)n--;return s>0||n<t.length?t.slice(s,n):t}(s,n,this.max);return this._unit=e.unit||(i.autoSkip?jo(e.minUnit,this.min,this.max,this._getLabelCapacity(n)):function(t,e,i,s,n){for(let o=Bo.length-1;o>=Bo.indexOf(i);o--){const i=Bo[o];if(Fo[i].common&&t._adapter.diff(n,s,i)>=e-1)return i}return Bo[i?Bo.indexOf(i):0]}(this,o.length,e.minUnit,this.min,this.max)),this._majorUnit=i.major.enabled&&"year"!==this._unit?function(t){for(let e=Bo.indexOf(t)+1,i=Bo.length;e<i;++e)if(Fo[Bo[e]].common)return Bo[e]}(this._unit):void 0,this.initOffsets(s),t.reverse&&o.reverse(),No(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map((t=>+t.value)))}initOffsets(t=[]){let e,i,s=0,n=0;this.options.offset&&t.length&&(e=this.getDecimalForValue(t[0]),s=1===t.length?1-e:(this.getDecimalForValue(t[1])-e)/2,i=this.getDecimalForValue(t[t.length-1]),n=1===t.length?i:(i-this.getDecimalForValue(t[t.length-2]))/2);const o=t.length<3?.5:.25;s=de(s,0,o),n=de(n,0,o),this._offsets={start:s,end:n,factor:1/(s+1+n)}}_generate(){const t=this._adapter,e=this.min,i=this.max,s=this.options,n=s.time,o=n.unit||jo(n.minUnit,e,i,this._getLabelCapacity(e)),r=Ct(s.ticks.stepSize,1),a="week"===o&&n.isoWeekday,h=ne(a)||!0===a,l={};let c,d,u=e;if(h&&(u=+t.startOf(u,"isoWeek",a)),u=+t.startOf(u,h?"day":o),t.diff(i,e,o)>1e5*r)throw new Error(e+" and "+i+" are too far apart with stepSize of "+r+" "+o);const f="data"===s.ticks.source&&this.getDataTimestamps();for(c=u,d=0;c<i;c=+t.add(c,r,o),d++)Vo(l,c,f);return c!==i&&"ticks"!==s.bounds&&1!==d||Vo(l,c,f),Object.keys(l).sort(Ho).map((t=>+t))}getLabelForValue(t){const e=this._adapter,i=this.options.time;return i.tooltipFormat?e.format(t,i.tooltipFormat):e.format(t,i.displayFormats.datetime)}format(t,e){const i=this.options.time.displayFormats,s=this._unit,n=e||i[s];return this._adapter.format(t,n)}_tickFormatFunction(t,e,i,s){const n=this.options,o=n.ticks.callback;if(o)return At(o,[t,e,i],this);const r=n.time.displayFormats,a=this._unit,h=this._majorUnit,l=a&&r[a],c=h&&r[h],d=i[e],u=h&&c&&d&&d.major;return this._adapter.format(t,s||(u?c:l))}generateTickLabels(t){let e,i,s;for(e=0,i=t.length;e<i;++e)s=t[e],s.label=this._tickFormatFunction(s.value,e,t)}getDecimalForValue(t){return null===t?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const e=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((e.start+i)*e.factor)}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const e=this.options.ticks,i=this.ctx.measureText(t).width,s=oe(this.isHorizontal()?e.maxRotation:e.minRotation),n=Math.cos(s),o=Math.sin(s),r=this._resolveTickFontOptions(0).size;return{w:i*n+r*o,h:i*o+r*n}}_getLabelCapacity(t){const e=this.options.time,i=e.displayFormats,s=i[e.unit]||i.millisecond,n=this._tickFormatFunction(t,0,No(this,[t],this._majorUnit),s),o=this._getLabelSize(n),r=Math.floor(this.isHorizontal()?this.width/o.w:this.height/o.h)-1;return r>0?r:1}getDataTimestamps(){let t,e,i=this._cache.data||[];if(i.length)return i;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(t=0,e=s.length;t<e;++t)i=i.concat(s[t].controller.getAllParsedValues(this));return this._cache.data=this.normalize(i)}getLabelTimestamps(){const t=this._cache.labels||[];let e,i;if(t.length)return t;const s=this.getLabels();for(e=0,i=s.length;e<i;++e)t.push(Wo(this,s[e]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return function(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}(t.sort(Ho))}}function Yo(t,e,i){let s,n,o,r,a=0,h=t.length-1;i?(e>=t[a].pos&&e<=t[h].pos&&({lo:a,hi:h}=ge(t,"pos",e)),({pos:s,time:o}=t[a]),({pos:n,time:r}=t[h])):(e>=t[a].time&&e<=t[h].time&&({lo:a,hi:h}=ge(t,"time",e)),({time:s,pos:o}=t[a]),({time:n,pos:r}=t[h]));const l=n-s;return l?o+(r-o)*(e-s)/l:o}i($o,"id","time"),i($o,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});class Uo extends $o{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),e=this._table=this.buildLookupTable(t);this._minPos=Yo(e,this.min),this._tableRange=Yo(e,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:e,max:i}=this,s=[],n=[];let o,r,a,h,l;for(o=0,r=t.length;o<r;++o)h=t[o],h>=e&&h<=i&&s.push(h);if(s.length<2)return[{time:e,pos:0},{time:i,pos:1}];for(o=0,r=s.length;o<r;++o)l=s[o+1],a=s[o-1],h=s[o],Math.round((l+a)/2)!==h&&n.push({time:h,pos:o/(r-1)});return n}_generate(){const t=this.min,e=this.max;let i=super.getDataTimestamps();return i.includes(t)&&i.length||i.splice(0,0,t),i.includes(e)&&1!==i.length||i.push(e),i.sort(((t,e)=>t-e))}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const e=this.getDataTimestamps(),i=this.getLabelTimestamps();return t=e.length&&i.length?this.normalize(e.concat(i)):e.length?e:i,t=this._cache.all=t,t}getDecimalForValue(t){return(Yo(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const e=this._offsets,i=this.getDecimalForPixel(t)/e.factor-e.end;return Yo(this._table,i*this._tableRange+this._minPos,!0)}}i(Uo,"id","timeseries"),i(Uo,"defaults",$o.defaults);const Xo="label";function qo(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function Ko(t,e){t.labels=e}function Go(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Xo;const s=[];t.datasets=e.map((e=>{const n=t.datasets.find((t=>t[i]===e[i]));return n&&e.data&&!s.includes(n)?(s.push(n),Object.assign(n,e),n):{...e}}))}function Zo(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Xo;const i={labels:[],datasets:[]};return Ko(i,t.labels),Go(i,t.datasets,e),i}function Qo(t,e){const{height:i=150,width:o=300,redraw:r=!1,datasetIdKey:a,type:h,data:l,options:c,plugins:d=[],fallbackContent:u,updateMode:f,...g}=t,p=s.useRef(null),m=s.useRef(),x=()=>{p.current&&(m.current=new Kn(p.current,{type:h,data:Zo(l,a),options:c&&{...c},plugins:d}),qo(e,m.current))},b=()=>{qo(e,null),m.current&&(m.current.destroy(),m.current=null)};return s.useEffect((()=>{!r&&m.current&&c&&function(t,e){const i=t.options;i&&e&&Object.assign(i,e)}(m.current,c)}),[r,c]),s.useEffect((()=>{!r&&m.current&&Ko(m.current.config.data,l.labels)}),[r,l.labels]),s.useEffect((()=>{!r&&m.current&&l.datasets&&Go(m.current.config.data,l.datasets,a)}),[r,l.datasets]),s.useEffect((()=>{m.current&&(r?(b(),setTimeout(x)):m.current.update(f))}),[r,c,l.labels,l.datasets,f]),s.useEffect((()=>{m.current&&(b(),setTimeout(x))}),[h]),s.useEffect((()=>(x(),()=>b())),[]),n.createElement("canvas",Object.assign({ref:p,role:"img",height:i,width:o},g),u)}const Jo=s.forwardRef(Qo);function tr(t,e){return Kn.register(e),s.forwardRef(((e,i)=>n.createElement(Jo,Object.assign({},e,{ref:i,type:t}))))}const er=tr("line",_s),ir={datetime:"MMM d, yyyy, h:mm:ss aaaa",millisecond:"h:mm:ss.SSS aaaa",second:"h:mm:ss aaaa",minute:"h:mm aaaa",hour:"ha",day:"MMM d",week:"PP",month:"MMM yyyy",quarter:"qqq - yyyy",year:"yyyy"};ws._date.override({_id:"date-fns",formats:function(){return ir},parse:function(t,e){if(null==t)return null;const i=typeof t;return"number"===i||t instanceof Date?t=o(t):"string"===i&&(t="string"==typeof e?r(t,e,new Date,this.options):a(t,this.options)),h(t)?t.getTime():null},format:function(t,e){return l(t,e,this.options)},add:function(t,e,i){switch(i){case"millisecond":return b(t,e);case"second":return x(t,e);case"minute":return m(t,e);case"hour":return p(t,e);case"day":return g(t,e);case"week":return f(t,e);case"month":return u(t,e);case"quarter":return d(t,e);case"year":return c(t,e);default:return t}},diff:function(t,e,i){switch(i){case"millisecond":return D(t,e);case"second":return O(t,e);case"minute":return S(t,e);case"hour":return M(t,e);case"day":return k(t,e);case"week":return w(t,e);case"month":return v(t,e);case"quarter":return y(t,e);case"year":return _(t,e);default:return 0}},startOf:function(t,e,i){switch(e){case"second":return z(t);case"minute":return E(t);case"hour":return L(t);case"day":return I(t);case"week":return A(t);case"isoWeek":return A(t,{weekStartsOn:+i});case"month":return C(t);case"quarter":return T(t);case"year":return P(t);default:return t}},endOf:function(t,e){switch(e){case"second":return N(t);case"minute":return V(t);case"hour":return j(t);case"day":return W(t);case"week":return H(t);case"month":return B(t);case"quarter":return F(t);case"year":return R(t);default:return t}}});export{Kn as C,Ro as L,ao as P,$o as T,Io as a,oo as b,To as c,uo as d,er as e,go as p};
