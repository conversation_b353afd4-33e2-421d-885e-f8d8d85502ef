import axios from 'axios';
import { User, Sensor, Permission } from '../types';
import { LoginCredentials, AuthResponse } from '../services/authService';
import { processApiTimestamp } from '../utils/dateFormat';

// Create an axios instance with default configuration
const axiosInstance = axios.create();

// Add a request interceptor to handle authentication headers
axiosInstance.interceptors.request.use(
  (config) => {
    // Retrieve the token from local storage
    const token = localStorage.getItem('token');
    // If a token exists, set it in the Authorization header
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    // Log any request errors
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors globally
axiosInstance.interceptors.response.use(
  (response) => response, // Return the response if successful
  (error) => {
    // Log any response errors (only in development)
    if (process.env.NODE_ENV !== 'production') {
      console.error('Response error:', error);
    }

    // If it's a 401 error but not from the login endpoint
    if (error.response?.status === 401 && !error.config.url.includes('/auth/login')) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Define the structure of the sensor data response
interface SensorDataResponse {
  salinity: number;
  do: number;
  ph: number;
  temp: number;
  cond: number;
  depth: number;
  turbidity: number;
  latitude: number;
  longitude: number;
  timestamp: string;
}

// Define the API object containing various endpoints
const api = {
  // Auth
  login: (credentials: LoginCredentials) => 
    axiosInstance.post<AuthResponse>('/api/auth/login', credentials),

  // User profile
  getUserProfile: () => axiosInstance.get<User>('/api/users/profile'),
  changePassword: (passwords: { currentPassword: string; newPassword: string }) =>
    axiosInstance.put('/api/users/change-password', passwords),

  // Admin-only endpoints
  getUsers: () => axiosInstance.get<User[]>('/api/users'),
  createUser: (user: User) => axiosInstance.post<User>('/api/users', user),
  updateUser: (username: string, user: User) => axiosInstance.put<User>(`/api/users/${username}`, user),
  deleteUser: (username: string) => axiosInstance.delete(`/api/users/${username}`),

  // User-specific endpoints
  getUserSensors: () => axiosInstance.get<Sensor[]>('/api/my-sensors'),
  getUserPermissions: () => axiosInstance.get<Permission[]>('/api/my-permissions'),

  // Sensors
  getSensors: () => axiosInstance.get<Sensor[]>('/api/sensors'),
  getSensorDetails: (equipmentId: string) => 
    axiosInstance.get<Sensor>(`/api/sensors/${equipmentId}`),
  createSensor: (sensor: Sensor) => axiosInstance.post<Sensor>('/api/sensors', sensor),
  updateSensor: (equipmentId: string, sensor: Sensor) => 
    axiosInstance.put<Sensor>(`/api/sensors/${equipmentId}`, sensor),
  deleteSensor: (equipmentId: string) => axiosInstance.delete(`/api/sensors/${equipmentId}`),

  // Permissions
  getPermissions: async () => {
    const response = await axiosInstance.get<Permission[]>('/api/permissions');
    return response;
  },
  // Removed getAllPermissions as the backend route /api/permissions/all does not exist
  createPermission: (permission: Permission) => 
    axiosInstance.post<Permission>('/api/permissions', permission),
  deletePermission: (userId: string, sensorId: string) => 
    axiosInstance.delete('/api/permissions', { 
      data: { userId, sensorId }
    }),

  // Fetch sensor data based on the provided API ID
  getSensorData: async (apiId: string): Promise<SensorDataResponse> => {
    try {
      // Use server-side proxy to avoid exposing API credentials in client
      const response = await axiosInstance.get(`/api/sensor-data/${apiId}`);
      return response.data;
    } catch (error) {
      // Log error without sensitive data in production
      if (process.env.NODE_ENV !== 'production') {
        console.error('Error in getSensorData:', error);
      }
      throw error;
    }
  },
};

// Export the API object for use in other modules
export { api };
