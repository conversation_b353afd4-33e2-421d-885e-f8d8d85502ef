import express from 'express';
import { User as UserModel, Sensor as SensorModel, Permission as PermissionModel } from '../database/schema'; // Alias models
import { User } from '../../types'; // Import actual types (Sensor, Permission removed)
import { validateUser, validateSensor, validatePasswordChange } from '../middleware/validation'; // validatePermission removed
import { authMiddleware } from '../middleware/auth';
import { requireAdmin } from '../middleware/roleAccess';
import { Request } from 'express'; // Import Request type
import bcrypt from 'bcrypt'; // Import bcrypt
import { cache, CACHE_KEYS, CACHE_TTL, cacheMiddleware, cacheUtils } from '../utils/cache';
import { Op } from 'sequelize';
import compression from 'compression';

// Define the structure of the user object attached by authMiddleware
interface AuthenticatedUser {
  id?: number; // Assuming id is available, adjust if needed
  username: string;
  role: 'admin' | 'user'; // Or other roles if applicable
}

// Extend the default Request type to include the user property
interface AuthenticatedRequest extends Request {
  user?: AuthenticatedUser;
}

const router = express.Router();

// Route to get all sensors (Requires authentication) - with caching
router.get('/sensors', authMiddleware, cacheMiddleware(CACHE_TTL.SENSOR_LIST), async (_req, res) => { // req -> _req
  try {
    // Fetch all sensors from the database
    const sensors = await SensorModel.findAll({ // Use alias
      raw: true,
      attributes: ['equipmentId', 'apiId', 'name', 'location', 'item', 'status']
    });
    // Respond with the list of sensors
    res.json(sensors);
  } catch (error) {
    console.error('Error fetching sensors:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch sensors' });
  }
});

// Route to get all users (admin only)
router.get('/users', authMiddleware, requireAdmin, async (_req, res) => { // req -> _req
  try {
    // Fetch all users from the database
    const users = await UserModel.findAll({ // Use alias
      attributes: [
        'username',
        'fullName',
        'email',
        'role',
        'status',
        'password',
        'lastActive'
      ],
      raw: true
    });
    // Respond with the list of users
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

// Route to get all permissions (Admin only)
router.get('/permissions', authMiddleware, requireAdmin, async (_req, res) => { // req -> _req
  try {
    // Fetch all permissions from the database, including associated users and sensors
    const permissions = await PermissionModel.findAll({ // Use alias
      include: [
        { model: UserModel, attributes: ['username', 'fullName'] }, // Use alias
        { model: SensorModel, attributes: ['equipmentId', 'name'] } // Use alias
      ]
    });
    // Respond with the list of permissions
    res.json(permissions);
  } catch (error) {
    console.error('Error fetching permissions:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch permissions' });
  }
});

// Route to create a new sensor (Admin only)
router.post('/sensors', authMiddleware, requireAdmin, validateSensor, async (req, res) => {
  try {
    // Create a new sensor using the request body
    const sensor = await SensorModel.create(req.body); // Use alias
    // Respond with the created sensor data
    res.json(sensor.get({ plain: true }));
  } catch (error) {
    console.error('Error creating sensor:', error);
    // Respond with an error message if creation fails
    res.status(500).json({ error: 'Failed to create sensor' });
  }
});

// Route to delete a sensor by equipmentId (Admin only)
router.delete('/sensors/:equipmentId', authMiddleware, requireAdmin, async (req, res) => {
  try {
    // Delete the sensor from the database
    await SensorModel.destroy({ // Use alias
      where: { equipmentId: req.params.equipmentId }
    });
    // Respond with success message
    res.json({ success: true });
  } catch (error) {
    // Respond with an error message if deletion fails
    res.status(500).json({ error: 'Failed to delete sensor' });
  }
});

// Route to create a new user (Admin only)
router.post('/users', authMiddleware, requireAdmin, validateUser, async (req, res) => {
  try {
    const userData = req.body; // Get user data from request body
    console.log('Creating user with data:', {
      ...userData,
      password: '***masked***' // Mask the password in logs
    });

    // Hash the password
    const saltRounds = 10; // Standard salt rounds
    const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

    // Create user with hashed password, remove actualPassword
    const user = await UserModel.create({ // Use alias
      ...userData,
      password: hashedPassword,
      // actualPassword: userData.password, // Removed insecure field
      lastActive: new Date().toISOString(),
      status: 'active'
    });

    // Get the created user data
    const createdUser = await UserModel.findOne({ // Use alias
      where: { username: user.get('username') },
      raw: true,
      attributes: [
        'username',
        'fullName',
        'email',
        'role',
        'status',
        // 'password', // Exclude password hash from response
        'lastActive'
      ]
    });

    console.log('User created successfully:', createdUser); // Log without password

    // Respond with the created user data (excluding password)
    res.json(createdUser);
  } catch (error) {
    console.error('Failed to create user:', error);
    // Respond with an error message if creation fails
    res.status(500).json({ error: 'Failed to create user' });
  }
});

// Route for user to change their own password
router.put('/users/change-password', authMiddleware, validatePasswordChange, async (req: AuthenticatedRequest, res) => {
  try {
    console.log('🔑 Password change request received for user:', req.user?.username);
    const { currentPassword, newPassword } = req.body;
    const username = req.user?.username;

    if (!username) {
      console.log('❌ Password change failed: User not authenticated');
      return res.status(401).json({ error: 'User not authenticated' });
    }

    // Find the user
    const user = await UserModel.findOne({ where: { username } });
    if (!user) {
      console.log(`❌ Password change failed: User ${username} not found`);
      return res.status(404).json({ error: 'User not found' });
    }

    // Verify current password
    const storedPassword = user.get('password');
    if (typeof storedPassword !== 'string') {
      // Handle case where password might not be set (shouldn't happen with validation, but good practice)
      console.error(`❌ User ${username} has no password stored.`);
      return res.status(500).json({ error: 'User record incomplete' });
    }

    console.log(`🔍 Verifying current password for user: ${username}`);
    const isMatch = await bcrypt.compare(currentPassword, storedPassword);
    if (!isMatch) {
      console.log(`❌ Password change failed: Incorrect current password for user ${username}`);
      return res.status(400).json({ error: 'Incorrect current password' });
    }

    // Hash the new password
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update the password in the database
    await UserModel.update(
      { password: hashedNewPassword },
      { where: { username } }
    );

    // Clear all cached data for this user after password change
    console.log(`🧹 Clearing cached data for user: ${username}`);

    // Clear specific user caches
    cache.delete(CACHE_KEYS.USER_PERMISSIONS(username));
    cache.delete(CACHE_KEYS.SENSOR_LIST(username));
    cache.delete(CACHE_KEYS.USER_PROFILE(username));

    // Clear all user-related cache entries using the new utility function
    const clearedCount = cacheUtils.clearUserCache(username);

    console.log(`🧹 Cleared ${clearedCount} total cache entries for user: ${username}`);
    console.log(`✅ Password successfully changed for user: ${username}`);

    res.json({
      message: 'Password updated successfully',
      cacheCleared: true
    });

  } catch (error) {
    console.error('Error changing password:', error);
    res.status(500).json({
      error: 'Failed to change password',
      details: 'An internal server error occurred. Please try again later.'
    });
  }
});


// Route to update an existing user by username (Requires authentication - TODO: Add logic for user updating self vs admin)
router.put('/users/:username', authMiddleware, async (req: AuthenticatedRequest, res) => {
  try {
    // Basic check: Allow admin to update anyone, or user to update themselves
    if (req.user?.role !== 'admin' && req.user?.username !== req.params.username) {
      return res.status(403).json({ error: 'Permission denied to update this user' });
    }

    const userData = req.body; // Get user data from request body
    const updateData: Partial<User> = { ...userData }; // Use imported User type

    // If a new password is provided, hash it and remove actualPassword
    if (userData.password) {
      const saltRounds = 10;
      updateData.password = await bcrypt.hash(userData.password, saltRounds);
      // delete updateData.actualPassword; // Ensure actualPassword is not set/updated
    } else {
      // Ensure password is not accidentally updated if not provided
      delete updateData.password;
    }
    // Ensure actualPassword is never updated or included
    delete updateData.actualPassword;


    // Update the user in the database
    await UserModel.update(updateData, { // Use alias
      where: { username: req.params.username }
    });

    // If password was updated, clear user's cached data
    if (userData.password) {
      console.log(`🧹 Admin updated password for user: ${req.params.username}, clearing cache`);

      // Clear specific user caches
      cache.delete(CACHE_KEYS.USER_PERMISSIONS(req.params.username));
      cache.delete(CACHE_KEYS.SENSOR_LIST(req.params.username));
      cache.delete(CACHE_KEYS.USER_PROFILE(req.params.username));

      // Clear all user-related cache entries
      const clearedCount = cacheUtils.clearUserCache(req.params.username);
      console.log(`🧹 Cleared ${clearedCount} cache entries for user: ${req.params.username}`);
    }

    // Get updated user data, excluding password hash
    const updatedUser = await UserModel.findOne({ // Use alias
      where: { username: req.params.username },
      attributes: { exclude: ['password', 'actualPassword'] }, // Exclude password fields
      raw: true
    });

    // Respond with the updated user data (excluding password)
    res.json(updatedUser);
  } catch (error) {
    // Respond with an error message if updating fails
    res.status(500).json({ error: 'Failed to update user' });
  }
});

// Route to delete a user by username (Admin only)
router.delete('/users/:username', authMiddleware, requireAdmin, async (req, res) => {
  try {
    // Delete the user from the database
    await UserModel.destroy({ // Use alias
      where: { username: req.params.username }
    });
    // Respond with success message
    res.json({ success: true });
  } catch (error) {
    // Respond with an error message if deletion fails
    res.status(500).json({ error: 'Failed to delete user' });
  }
});

// Route to create a new permission (Admin only)
router.post('/permissions', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { userId, sensorId, view } = req.body; // Get permission data from request body
    console.log('Creating permission:', { userId, sensorId, view });

    // Validate input
    if (!userId || !sensorId) {
      return res.status(400).json({ error: 'userId and sensorId are required' });
    }

    // Check if user exists
    const user = await UserModel.findOne({ where: { username: userId } }); // Use alias
    if (!user) {
      console.log('User not found:', userId);
      return res.status(404).json({ error: 'User not found' });
    }

    // Check if sensor exists
    const sensor = await SensorModel.findOne({ where: { equipmentId: sensorId } }); // Use alias
    if (!sensor) {
      console.log('Sensor not found:', sensorId);
      return res.status(404).json({ error: 'Sensor not found' });
    }

    // Check if exact permission combination already exists
    const existingPermission = await PermissionModel.findOne({ // Use alias
      where: {
        userId,
        sensorId
      }
    });

    if (existingPermission) {
      console.log('Permission already exists, updating view status');
      // Update existing permission's view status
      await existingPermission.update({ view });

      // Fetch updated permission with associations
      const updatedPermission = await PermissionModel.findOne({ // Use alias
        where: { id: existingPermission.get('id') },
        include: [
          { model: UserModel, attributes: ['username', 'fullName'] }, // Use alias
          { model: SensorModel, attributes: ['equipmentId', 'name'] } // Use alias
        ]
      });

      // Respond with the updated permission data
      return res.json(updatedPermission);
    }

    // Create new permission
    console.log('Creating new permission');
    const permission = await PermissionModel.create({ // Use alias
      userId,
      sensorId,
      view: view ?? true
    });

    // Fetch created permission with associations
    const createdPermission = await PermissionModel.findOne({ // Use alias
      where: { id: permission.get('id') },
      include: [
        { model: UserModel, attributes: ['username', 'fullName'] }, // Use alias
        { model: SensorModel, attributes: ['equipmentId', 'name'] } // Use alias
      ]
    });

    // Clear user's permission cache after creating/updating permission
    console.log(`🧹 Permission created/updated for user: ${userId}, clearing permission cache`);
    cache.delete(CACHE_KEYS.USER_PERMISSIONS(userId));
    cache.delete(CACHE_KEYS.SENSOR_LIST(userId));

    console.log('Permission created successfully:', createdPermission?.get({ plain: true }));
    // Respond with the created permission data
    res.status(201).json(createdPermission);

  } catch (error) {
    console.error('Error creating permission:', error);
    // Respond with an error message if creation fails
    res.status(500).json({ error: 'Failed to create permission' });
  }
});

// Route to delete a permission (Admin only)
router.delete('/permissions', authMiddleware, requireAdmin, async (req, res) => {
  try {
    const { userId, sensorId } = req.body; // Get userId and sensorId from request body

    // Delete the permission from the database
    await PermissionModel.destroy({ // Use alias
      where: { userId, sensorId }
    });

    // Clear user's permission cache after deleting permission
    console.log(`🧹 Permission deleted for user: ${userId}, clearing permission cache`);
    cache.delete(CACHE_KEYS.USER_PERMISSIONS(userId));
    cache.delete(CACHE_KEYS.SENSOR_LIST(userId));

    // Respond with success message
    res.json({ success: true });
  } catch (error) {
    console.error('Error deleting permission:', error);
    // Respond with an error message if deletion fails
    res.status(500).json({ error: 'Failed to delete permission' });
  }
});

// Route to get sensor details with permission check
router.get('/sensors/:equipmentId', authMiddleware, async (req: AuthenticatedRequest, res) => { // Added authMiddleware and type
  try {
    const { equipmentId } = req.params; // Extract equipmentId from request parameters
    const userId = req.user?.username; // Use username as per AuthenticatedUser interface

    // Check permission if user is not admin
    if (req.user?.role !== 'admin') {
      const permission = await PermissionModel.findOne({ // Use alias
        where: {
          userId,
          sensorId: equipmentId,
          view: true
        }
      });

      // Deny access if permission is not found
      if (!permission) {
        return res.status(403).json({ error: 'Access denied to this sensor' });
      }
    }

    // Fetch the sensor data along with its permissions
    const sensor = await SensorModel.findOne({ // Use alias
      where: { equipmentId },
      include: [{
        model: PermissionModel, // Use alias
        attributes: ['userId', 'view'],
        required: false
      }],
      raw: true,
      nest: true
    });

    // Check if the sensor was found
    if (!sensor) {
      return res.status(404).json({ error: 'Sensor not found' });
    }

    // Respond with the sensor data
    res.json(sensor);
  } catch (error) {
    console.error('Error fetching sensor:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch sensor' });
  }
});

// Route to get user's permissions (Requires authentication - TODO: Add logic for user getting self vs admin getting others)
router.get('/permissions/user/:userId', authMiddleware, async (req: AuthenticatedRequest, res) => {
  try {
    // Basic check: Allow admin to get anyone's permissions, or user to get their own
    if (req.user?.role !== 'admin' && req.user?.username !== req.params.userId) {
       return res.status(403).json({ error: 'Permission denied to view these permissions' });
    }
    const { userId } = req.params; // Extract userId from request parameters
    // Fetch permissions for the specified user
    const permissions = await PermissionModel.findAll({ // Use alias
      where: { userId, view: true },
      include: [{
        model: SensorModel, // Use alias
        attributes: ['equipmentId', 'name']
      }],
      raw: true,
      nest: true
    });
    // Respond with the list of permissions
    res.json(permissions);
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch permissions' });
  }
});

// Route to update sensor with permission check
router.put('/sensors/:equipmentId', authMiddleware, async (req: AuthenticatedRequest, res) => { // Added authMiddleware and type
  try {
    const { equipmentId } = req.params; // Extract equipmentId from request parameters
    const userId = req.user?.username; // Use username as per AuthenticatedUser interface

    // Check permission if user is not admin
    if (req.user?.role !== 'admin') {
      const permission = await PermissionModel.findOne({ // Use alias
        where: {
          userId,
          sensorId: equipmentId,
          view: true
        }
      });

      // Deny access if permission is not found
      if (!permission) {
        return res.status(403).json({ error: 'Access denied to this sensor' });
      }
    }

    const updateData = req.body; // Get update data from request body
    // Update the sensor in the database
    await SensorModel.update(updateData, { // Use alias
      where: { equipmentId }
    });

    // Fetch the updated sensor data
    const updatedSensor = await SensorModel.findOne({ // Use alias
      where: { equipmentId },
      include: [{
        model: PermissionModel, // Use alias
        attributes: ['userId', 'view'],
        required: false
      }],
      raw: true,
      nest: true
    });

    // Respond with the updated sensor data
    res.json(updatedSensor);
  } catch (error) {
    console.error('Error updating sensor:', error);
    // Respond with an error message if updating fails
    res.status(500).json({ error: 'Failed to update sensor' });
  }
});

// User-specific route to get sensors
router.get('/my-sensors', authMiddleware, async (req: any, res) => {
  try {
    const username = req.user?.username; // Get username from authenticated user
    const role = req.user?.role; // Get role from authenticated user

    // If admin, return all sensors
    if (role === 'admin') {
      const sensors = await SensorModel.findAll({ // Use alias
        raw: true,
        attributes: ['equipmentId', 'apiId', 'name', 'location', 'item', 'status']
      });
      return res.json(sensors);
    }

    // For regular users, get permitted sensors
    const permissions = await PermissionModel.findAll({ // Use alias
      where: {
        userId: username,
        view: true
      },
      include: [{
        model: SensorModel, // Use alias
        required: true,
        attributes: ['equipmentId', 'apiId', 'name', 'location', 'item', 'status']
      }]
    });

    const sensors = permissions.map(p => p.get({ plain: true }).Sensor);
    // Respond with the list of permitted sensors
    res.json(sensors);
  } catch (error) {
    console.error('Error fetching user sensors:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch sensors' });
  }
});

// Route to get user profile
router.get('/users/profile', authMiddleware, async (req: any, res) => {
  try {
    const username = req.user?.username; // Get username from authenticated user

    // Check if user is authenticated
    if (!username) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    // Fetch user profile data
    const user = await UserModel.findOne({ // Use alias
      where: { username },
      attributes: ['username', 'fullName', 'email', 'role', 'status', 'lastActive'],
      raw: true
    });

    // Check if the user was found
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Respond with the user profile data
    res.json(user);
  } catch (error) {
    console.error('Error fetching user profile:', error);
    // Respond with an error message if fetching fails
    res.status(500).json({ error: 'Failed to fetch user profile' });
  }
});

export default router;
