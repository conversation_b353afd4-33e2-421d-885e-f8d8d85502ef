# Water Quality Network Dashboard - Production Environment Configuration
# This file contains production-specific environment variables

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL database URL (recommended for production)
DATABASE_URL=postgresql://username:password@localhost:5432/wqn_dashboard

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret - MUST be at least 64 characters long for production
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=CHANGE_THIS_TO_A_SECURE_64_CHARACTER_RANDOM_STRING_FOR_PRODUCTION_USE
JWT_EXPIRES_IN=24h
JWT_ISSUER=wqn-dashboard
JWT_AUDIENCE=wqn-users

# Session Secret - MUST be at least 64 characters long for production
# Generate with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
SESSION_SECRET=CHANGE_THIS_TO_A_SECURE_64_CHARACTER_RANDOM_STRING_FOR_PRODUCTION_USE

# bcrypt salt rounds (12-15 recommended for production)
BCRYPT_ROUNDS=14

# =============================================================================
# EXTERNAL API CONFIGURATION (Boqucloud)
# =============================================================================
# SECURITY WARNING: These credentials should be set via environment variables
# in production deployment, NOT in this file!
#
# Set these in your production environment:
# export VITE_API_KEY=your_actual_api_key
# export VITE_API_SECRET=your_actual_api_secret
# export VITE_ACCOUNT=your_actual_account
#
# VITE_API_KEY=
# VITE_API_SECRET=
# VITE_ACCOUNT=

# Boqucloud API endpoints (these can be in the file as they're not sensitive)
VITE_TOKEN_URL=https://vip.boqucloud.com/api/token/get
VITE_MONITOR_OPEN_URL=https://vip.boqucloud.com/api/eg/monitor/open
VITE_SENSOR_DATA_URL=https://vip.boqucloud.com/api/eg/signal/value

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
# Server port (use 80 for production or behind reverse proxy)
PORT=3001

# Server host binding (0.0.0.0 for all interfaces, 127.0.0.1 for localhost only)
# Use 0.0.0.0 in production to allow external access
HOST=0.0.0.0

# Node environment
NODE_ENV=production

# CORS allowed origins (update with your production domain)
# Example: https://your-domain.com,https://www.your-domain.com
CORS_ORIGIN=https://your-production-domain.com

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Enable compression
ENABLE_COMPRESSION=true

# Cache settings
CACHE_TTL=3600
REDIS_URL=redis://localhost:6379

# Rate limiting (requests per window)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# SECURITY HEADERS & FEATURES
# =============================================================================
# Security features
ENABLE_HELMET=true
ENABLE_RATE_LIMITING=true
ENABLE_CORS=true

# Content Security Policy
CSP_ENABLED=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Log file path
LOG_FILE=./logs/production.log

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Production feature flags
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true
ENABLE_EXPORT=true
VITE_DEV_MODE=false

# =============================================================================
# SSL/TLS CONFIGURATION (if handling SSL at app level)
# =============================================================================
# SSL_CERT_PATH=/path/to/certificate.crt
# SSL_KEY_PATH=/path/to/private.key
# FORCE_HTTPS=true

# =============================================================================
# MONITORING & HEALTH CHECKS
# =============================================================================
# Health check endpoint
HEALTH_CHECK_ENABLED=true

# Metrics collection
ENABLE_METRICS=true

# =============================================================================
# EMAIL CONFIGURATION (for notifications)
# =============================================================================
# SMTP configuration for production email notifications
# SMTP_HOST=smtp.your-provider.com
# SMTP_PORT=587
# SMTP_SECURE=true
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-secure-app-password
