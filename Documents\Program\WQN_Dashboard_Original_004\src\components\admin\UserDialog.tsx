import React, { useState } from 'react';
import { X } from 'lucide-react';
import { User } from '../../types';

// Interface defining the props for the UserDialog component
interface UserDialogProps {
  user: User; // The user object being edited or added
  onSubmit: (e: React.FormEvent) => void; // Function to call on form submission
  onCancel: () => void; // Function to call when the dialog is canceled
  isAdding: boolean; // Flag indicating if the dialog is for adding a new user
  setEditingUser: (user: User) => void; // Function to set the user being edited
}

// UserDialog component for adding or editing a user
export const UserDialog: React.FC<UserDialogProps> = ({ 
  user, 
  onSubmit, 
  onCancel, 
  isAdding,
  setEditingUser 
}) => {
  const [passwordError, setPasswordError] = useState(''); // State for password error messages
  const [confirmPassword, setConfirmPassword] = useState(''); // State for confirming the password

  // Handle changes in input fields
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target; // Destructure name and value from the event target
    setEditingUser({ ...user, [name]: value }); // Update the user object with the new value
    
    // Clear password error when user types in the password field
    if (name === 'password') {
      setPasswordError('');
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault(); // Prevent default form submission behavior

    // Validate required fields
    if (!user.username.trim()) {
      setPasswordError('Username is required');
      return;
    }

    if (!user.fullName.trim()) {
      setPasswordError('Full name is required');
      return;
    }

    if (!user.email.trim()) {
      setPasswordError('Email is required');
      return;
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(user.email)) {
      setPasswordError('Please enter a valid email address');
      return;
    }

    // Password validation for new users or when password is being changed
    if (isAdding || user.password.trim()) {
      // Match backend validation requirements
      if (user.password.length < 8) {
        setPasswordError('Password must be at least 8 characters long');
        return;
      }

      if (!/[A-Z]/.test(user.password)) {
        setPasswordError('Password must contain at least one uppercase letter');
        return;
      }

      if (!/[a-z]/.test(user.password)) {
        setPasswordError('Password must contain at least one lowercase letter');
        return;
      }

      if (!/[0-9]/.test(user.password)) {
        setPasswordError('Password must contain at least one number');
        return;
      }

      if (!/[^A-Za-z0-9]/.test(user.password)) {
        setPasswordError('Password must contain at least one special character');
        return;
      }

      if (isAdding && user.password !== confirmPassword) {
        setPasswordError('Passwords do not match');
        return;
      }
    }

    onSubmit(e); // Call the onSubmit function passed in props
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold">{isAdding ? 'Add User' : 'Edit User'}</h2> {/* Title based on mode */}
          <button onClick={onCancel} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" /> {/* Close button */}
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Input for Username */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Username</label>
            <input
              type="text"
              name="username"
              value={user.username}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
              disabled={!isAdding} // Disable if not adding
            />
          </div>

          {/* Input for Full Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Full Name</label>
            <input
              type="text"
              name="fullName"
              value={user.fullName}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          {/* Input for Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              name="email"
              value={user.email}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
            />
          </div>

          {/* Select for User Role */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Role</label>
            <select
              name="role"
              value={user.role}
              onChange={handleChange}
              className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
              required
            >
              <option value="user">User</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          {/* Input for Password */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              name="password"
              value={user.password}
              onChange={handleChange}
              className={`mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                passwordError ? 'border-red-300' : 'border-gray-300'
              }`}
              required={isAdding} // Required if adding a new user
              placeholder={isAdding ? 'Enter password' : 'Leave blank to keep current password'}
            />
            {isAdding && (
              <p className="mt-1 text-xs text-gray-500">
                Password must be at least 8 characters with uppercase, lowercase, number, and special character
              </p>
            )}
          </div>

          {/* Input for Confirm Password, shown only when adding a user */}
          {isAdding && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Confirm Password</label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)} // Update confirm password state
                className={`mt-1 block w-full rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 ${
                  passwordError ? 'border-red-300' : 'border-gray-300'
                }`}
                required={isAdding} // Required if adding a new user
                placeholder="Confirm password"
              />
            </div>
          )}

          {/* Display password error message if any */}
          {passwordError && (
            <div className="text-red-500 text-sm">{passwordError}</div>
          )}

          {/* Buttons for Cancel and Submit */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel} // Call onCancel when clicked
              className="flex-1 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit" // Submit the form
              className="flex-1 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {isAdding ? 'Add User' : 'Update User'} {/* Button text based on mode */}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};